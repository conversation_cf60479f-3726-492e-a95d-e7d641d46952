{"name": "eslint-plugin-tailwindcss", "version": "3.18.2", "description": "Rules enforcing best practices while using Tailwind CSS", "keywords": ["eslint", "eslintplugin", "eslint-plugin", "tailwind", "tailwindcss"], "author": "<PERSON>", "repository": {"type": "git", "url": "https://github.com/francoismassart/eslint-plugin-tailwindcss"}, "homepage": "https://github.com/francoismassart/eslint-plugin-tailwindcss", "bugs": "https://github.com/francoismassart/eslint-plugin-tailwindcss/issues", "main": "lib/index.js", "scripts": {"test": "npm run test:base && npm run test:integration", "test:base": "mocha \"tests/lib/**/*.js\"", "test:integration": "mocha \"tests/integrations/*.js\" --timeout 60000"}, "files": ["lib"], "peerDependencies": {"tailwindcss": "^3.4.0"}, "dependencies": {"fast-glob": "^3.2.5", "postcss": "^8.4.4"}, "devDependencies": {"@angular-eslint/template-parser": "^15.2.0", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.3", "@tailwindcss/line-clamp": "^0.4.2", "@tailwindcss/typography": "^0.5.8", "@typescript-eslint/parser": "^5.50.0", "autoprefixer": "^10.4.0", "daisyui": "^2.6.4", "eslint": "^8.57.0", "mocha": "^10.2.0", "semver": "^7.6.0", "tailwindcss": "^3.4.0", "typescript": "4.3.5", "vue-eslint-parser": "^9.4.2"}, "packageManager": "npm@10.2.5+sha256.8002e3e7305d2abd4016e1368af48d49b066c269079eeb10a56e7d6598acfdaa", "engines": {"node": ">=18.12.0"}, "license": "MIT"}