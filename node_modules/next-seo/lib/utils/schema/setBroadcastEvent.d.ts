import type { BroadcastEvent } from 'src/types';
export declare function setBroadcastEvent(publication?: BroadcastEvent | BroadcastEvent[]): {
    '@type': string;
    name?: string | undefined;
    isLiveBroadcast?: boolean | undefined;
    startDate?: string | undefined;
    endDate?: string | undefined;
} | {
    '@type': string;
    name?: string | undefined;
    isLiveBroadcast?: boolean | undefined;
    startDate?: string | undefined;
    endDate?: string | undefined;
}[] | undefined;
