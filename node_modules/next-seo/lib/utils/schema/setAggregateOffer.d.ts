import type { AggregateOffer } from 'src/types';
export declare function setAggregateOffer(aggregateOffer?: AggregateOffer): {
    '@type': string;
    priceCurrency: string;
    highPrice: string | undefined;
    lowPrice: string;
    offerCount: string | undefined;
    offers: {
        hasMerchantReturnPolicy?: {
            returnPolicySeasonalOverride?: import("src/types").ReturnPolicySeasonalOverrides[] | {
                length: number;
                toString(): string;
                toLocaleString(): string;
                pop(): import("src/types").ReturnPolicySeasonalOverrides | undefined;
                push(...items: import("src/types").ReturnPolicySeasonalOverrides[]): number;
                concat(...items: ConcatArray<import("src/types").ReturnPolicySeasonalOverrides>[]): import("src/types").ReturnPolicySeasonalOverrides[];
                concat(...items: (import("src/types").ReturnPolicySeasonalOverrides | ConcatArray<import("src/types").ReturnPolicySeasonalOverrides>)[]): import("src/types").ReturnPolicySeasonalOverrides[];
                join(separator?: string | undefined): string;
                reverse(): import("src/types").ReturnPolicySeasonalOverrides[];
                shift(): import("src/types").ReturnPolicySeasonalOverrides | undefined;
                slice(start?: number | undefined, end?: number | undefined): import("src/types").ReturnPolicySeasonalOverrides[];
                sort(compareFn?: ((a: import("src/types").ReturnPolicySeasonalOverrides, b: import("src/types").ReturnPolicySeasonalOverrides) => number) | undefined): import("src/types").ReturnPolicySeasonalOverrides[];
                splice(start: number, deleteCount?: number | undefined): import("src/types").ReturnPolicySeasonalOverrides[];
                splice(start: number, deleteCount: number, ...items: import("src/types").ReturnPolicySeasonalOverrides[]): import("src/types").ReturnPolicySeasonalOverrides[];
                unshift(...items: import("src/types").ReturnPolicySeasonalOverrides[]): number;
                indexOf(searchElement: import("src/types").ReturnPolicySeasonalOverrides, fromIndex?: number | undefined): number;
                lastIndexOf(searchElement: import("src/types").ReturnPolicySeasonalOverrides, fromIndex?: number | undefined): number;
                every<S extends import("src/types").ReturnPolicySeasonalOverrides>(predicate: (value: import("src/types").ReturnPolicySeasonalOverrides, index: number, array: import("src/types").ReturnPolicySeasonalOverrides[]) => value is S, thisArg?: any): this is S[];
                every(predicate: (value: import("src/types").ReturnPolicySeasonalOverrides, index: number, array: import("src/types").ReturnPolicySeasonalOverrides[]) => unknown, thisArg?: any): boolean;
                some(predicate: (value: import("src/types").ReturnPolicySeasonalOverrides, index: number, array: import("src/types").ReturnPolicySeasonalOverrides[]) => unknown, thisArg?: any): boolean;
                forEach(callbackfn: (value: import("src/types").ReturnPolicySeasonalOverrides, index: number, array: import("src/types").ReturnPolicySeasonalOverrides[]) => void, thisArg?: any): void;
                map<U>(callbackfn: (value: import("src/types").ReturnPolicySeasonalOverrides, index: number, array: import("src/types").ReturnPolicySeasonalOverrides[]) => U, thisArg?: any): U[];
                filter<S_1 extends import("src/types").ReturnPolicySeasonalOverrides>(predicate: (value: import("src/types").ReturnPolicySeasonalOverrides, index: number, array: import("src/types").ReturnPolicySeasonalOverrides[]) => value is S_1, thisArg?: any): S_1[];
                filter(predicate: (value: import("src/types").ReturnPolicySeasonalOverrides, index: number, array: import("src/types").ReturnPolicySeasonalOverrides[]) => unknown, thisArg?: any): import("src/types").ReturnPolicySeasonalOverrides[];
                reduce(callbackfn: (previousValue: import("src/types").ReturnPolicySeasonalOverrides, currentValue: import("src/types").ReturnPolicySeasonalOverrides, currentIndex: number, array: import("src/types").ReturnPolicySeasonalOverrides[]) => import("src/types").ReturnPolicySeasonalOverrides): import("src/types").ReturnPolicySeasonalOverrides;
                reduce(callbackfn: (previousValue: import("src/types").ReturnPolicySeasonalOverrides, currentValue: import("src/types").ReturnPolicySeasonalOverrides, currentIndex: number, array: import("src/types").ReturnPolicySeasonalOverrides[]) => import("src/types").ReturnPolicySeasonalOverrides, initialValue: import("src/types").ReturnPolicySeasonalOverrides): import("src/types").ReturnPolicySeasonalOverrides;
                reduce<U_1>(callbackfn: (previousValue: U_1, currentValue: import("src/types").ReturnPolicySeasonalOverrides, currentIndex: number, array: import("src/types").ReturnPolicySeasonalOverrides[]) => U_1, initialValue: U_1): U_1;
                reduceRight(callbackfn: (previousValue: import("src/types").ReturnPolicySeasonalOverrides, currentValue: import("src/types").ReturnPolicySeasonalOverrides, currentIndex: number, array: import("src/types").ReturnPolicySeasonalOverrides[]) => import("src/types").ReturnPolicySeasonalOverrides): import("src/types").ReturnPolicySeasonalOverrides;
                reduceRight(callbackfn: (previousValue: import("src/types").ReturnPolicySeasonalOverrides, currentValue: import("src/types").ReturnPolicySeasonalOverrides, currentIndex: number, array: import("src/types").ReturnPolicySeasonalOverrides[]) => import("src/types").ReturnPolicySeasonalOverrides, initialValue: import("src/types").ReturnPolicySeasonalOverrides): import("src/types").ReturnPolicySeasonalOverrides;
                reduceRight<U_2>(callbackfn: (previousValue: U_2, currentValue: import("src/types").ReturnPolicySeasonalOverrides, currentIndex: number, array: import("src/types").ReturnPolicySeasonalOverrides[]) => U_2, initialValue: U_2): U_2;
                find<S_2 extends import("src/types").ReturnPolicySeasonalOverrides>(predicate: (value: import("src/types").ReturnPolicySeasonalOverrides, index: number, obj: import("src/types").ReturnPolicySeasonalOverrides[]) => value is S_2, thisArg?: any): S_2 | undefined;
                find(predicate: (value: import("src/types").ReturnPolicySeasonalOverrides, index: number, obj: import("src/types").ReturnPolicySeasonalOverrides[]) => unknown, thisArg?: any): import("src/types").ReturnPolicySeasonalOverrides | undefined;
                findIndex(predicate: (value: import("src/types").ReturnPolicySeasonalOverrides, index: number, obj: import("src/types").ReturnPolicySeasonalOverrides[]) => unknown, thisArg?: any): number;
                fill(value: import("src/types").ReturnPolicySeasonalOverrides, start?: number | undefined, end?: number | undefined): import("src/types").ReturnPolicySeasonalOverrides[];
                copyWithin(target: number, start?: number | undefined, end?: number | undefined): import("src/types").ReturnPolicySeasonalOverrides[];
                entries(): IterableIterator<[number, import("src/types").ReturnPolicySeasonalOverrides]>;
                keys(): IterableIterator<number>;
                values(): IterableIterator<import("src/types").ReturnPolicySeasonalOverrides>;
                includes(searchElement: import("src/types").ReturnPolicySeasonalOverrides, fromIndex?: number | undefined): boolean;
                flatMap<U_3, This = undefined>(callback: (this: This, value: import("src/types").ReturnPolicySeasonalOverrides, index: number, array: import("src/types").ReturnPolicySeasonalOverrides[]) => U_3 | readonly U_3[], thisArg?: This | undefined): U_3[];
                flat<A, D extends number = 1>(this: A, depth?: D | undefined): FlatArray<A, D>[];
                at(index: number): import("src/types").ReturnPolicySeasonalOverrides | undefined;
                findLast<S_3 extends import("src/types").ReturnPolicySeasonalOverrides>(predicate: (value: import("src/types").ReturnPolicySeasonalOverrides, index: number, array: import("src/types").ReturnPolicySeasonalOverrides[]) => value is S_3, thisArg?: any): S_3 | undefined;
                findLast(predicate: (value: import("src/types").ReturnPolicySeasonalOverrides, index: number, array: import("src/types").ReturnPolicySeasonalOverrides[]) => unknown, thisArg?: any): import("src/types").ReturnPolicySeasonalOverrides | undefined;
                findLastIndex(predicate: (value: import("src/types").ReturnPolicySeasonalOverrides, index: number, array: import("src/types").ReturnPolicySeasonalOverrides[]) => unknown, thisArg?: any): number;
                [Symbol.iterator](): IterableIterator<import("src/types").ReturnPolicySeasonalOverrides>;
                [Symbol.unscopables]: {
                    [x: number]: boolean | undefined;
                    length?: boolean | undefined;
                    toString?: boolean | undefined;
                    toLocaleString?: boolean | undefined;
                    pop?: boolean | undefined;
                    push?: boolean | undefined;
                    concat?: boolean | undefined;
                    join?: boolean | undefined;
                    reverse?: boolean | undefined;
                    shift?: boolean | undefined;
                    slice?: boolean | undefined;
                    sort?: boolean | undefined;
                    splice?: boolean | undefined;
                    unshift?: boolean | undefined;
                    indexOf?: boolean | undefined;
                    lastIndexOf?: boolean | undefined;
                    every?: boolean | undefined;
                    some?: boolean | undefined;
                    forEach?: boolean | undefined;
                    map?: boolean | undefined;
                    filter?: boolean | undefined;
                    reduce?: boolean | undefined;
                    reduceRight?: boolean | undefined;
                    find?: boolean | undefined;
                    findIndex?: boolean | undefined;
                    fill?: boolean | undefined;
                    copyWithin?: boolean | undefined;
                    entries?: boolean | undefined;
                    keys?: boolean | undefined;
                    values?: boolean | undefined;
                    includes?: boolean | undefined;
                    flatMap?: boolean | undefined;
                    flat?: boolean | undefined;
                    at?: boolean | undefined;
                    findLast?: boolean | undefined;
                    findLastIndex?: boolean | undefined;
                    [Symbol.iterator]?: boolean | undefined;
                    readonly [Symbol.unscopables]?: boolean | undefined;
                };
                '@type': string;
            } | undefined;
            returnPolicyCategory?: import("src/types").ReturnPolicyCategory | undefined;
            returnMethod?: import("src/types").ReturnMethod | import("src/types").ReturnMethod[] | undefined;
            returnFees?: import("src/types").ReturnFees | import("src/types").ReturnFees[] | undefined;
            refundType?: import("src/types").RefundType | import("src/types").RefundType[] | undefined;
            applicableCountry?: string | string[] | undefined;
            returnPolicyCountry?: string | string[] | undefined;
            merchantReturnLink?: string | undefined;
            itemCondition?: import("src/types").ItemCondition | import("src/types").ItemCondition[] | undefined;
            inStoreReturnsOffered?: boolean | undefined;
            restockingFee?: string | number | undefined;
            returnShippingFeesAmount?: {
                value: number;
                currency: string;
            } | undefined;
            customerRemorseReturnFees?: import("src/types").ReturnFees | import("src/types").ReturnFees[] | undefined;
            customerRemorseReturnLabelSource?: import("src/types").ReturnLabelSource | undefined;
            itemDefectReturnFees?: import("src/types").ReturnFees | import("src/types").ReturnFees[] | undefined;
            itemDefectReturnLabelSource?: import("src/types").ReturnLabelSource | undefined;
            '@type': string;
        } | undefined;
        seller: {
            '@type': string;
            name: string;
        };
        '@type': string;
        price: string;
        priceCurrency: string;
        priceValidUntil?: string | undefined;
        itemCondition?: string | undefined;
        availability?: string | undefined;
        url?: string | undefined;
        validFrom?: string | undefined;
    } | {
        hasMerchantReturnPolicy?: {
            returnPolicySeasonalOverride?: import("src/types").ReturnPolicySeasonalOverrides[] | {
                length: number;
                toString(): string;
                toLocaleString(): string;
                pop(): import("src/types").ReturnPolicySeasonalOverrides | undefined;
                push(...items: import("src/types").ReturnPolicySeasonalOverrides[]): number;
                concat(...items: ConcatArray<import("src/types").ReturnPolicySeasonalOverrides>[]): import("src/types").ReturnPolicySeasonalOverrides[];
                concat(...items: (import("src/types").ReturnPolicySeasonalOverrides | ConcatArray<import("src/types").ReturnPolicySeasonalOverrides>)[]): import("src/types").ReturnPolicySeasonalOverrides[];
                join(separator?: string | undefined): string;
                reverse(): import("src/types").ReturnPolicySeasonalOverrides[];
                shift(): import("src/types").ReturnPolicySeasonalOverrides | undefined;
                slice(start?: number | undefined, end?: number | undefined): import("src/types").ReturnPolicySeasonalOverrides[];
                sort(compareFn?: ((a: import("src/types").ReturnPolicySeasonalOverrides, b: import("src/types").ReturnPolicySeasonalOverrides) => number) | undefined): import("src/types").ReturnPolicySeasonalOverrides[];
                splice(start: number, deleteCount?: number | undefined): import("src/types").ReturnPolicySeasonalOverrides[];
                splice(start: number, deleteCount: number, ...items: import("src/types").ReturnPolicySeasonalOverrides[]): import("src/types").ReturnPolicySeasonalOverrides[];
                unshift(...items: import("src/types").ReturnPolicySeasonalOverrides[]): number;
                indexOf(searchElement: import("src/types").ReturnPolicySeasonalOverrides, fromIndex?: number | undefined): number;
                lastIndexOf(searchElement: import("src/types").ReturnPolicySeasonalOverrides, fromIndex?: number | undefined): number;
                every<S extends import("src/types").ReturnPolicySeasonalOverrides>(predicate: (value: import("src/types").ReturnPolicySeasonalOverrides, index: number, array: import("src/types").ReturnPolicySeasonalOverrides[]) => value is S, thisArg?: any): this is S[];
                every(predicate: (value: import("src/types").ReturnPolicySeasonalOverrides, index: number, array: import("src/types").ReturnPolicySeasonalOverrides[]) => unknown, thisArg?: any): boolean;
                some(predicate: (value: import("src/types").ReturnPolicySeasonalOverrides, index: number, array: import("src/types").ReturnPolicySeasonalOverrides[]) => unknown, thisArg?: any): boolean;
                forEach(callbackfn: (value: import("src/types").ReturnPolicySeasonalOverrides, index: number, array: import("src/types").ReturnPolicySeasonalOverrides[]) => void, thisArg?: any): void;
                map<U>(callbackfn: (value: import("src/types").ReturnPolicySeasonalOverrides, index: number, array: import("src/types").ReturnPolicySeasonalOverrides[]) => U, thisArg?: any): U[];
                filter<S_1 extends import("src/types").ReturnPolicySeasonalOverrides>(predicate: (value: import("src/types").ReturnPolicySeasonalOverrides, index: number, array: import("src/types").ReturnPolicySeasonalOverrides[]) => value is S_1, thisArg?: any): S_1[];
                filter(predicate: (value: import("src/types").ReturnPolicySeasonalOverrides, index: number, array: import("src/types").ReturnPolicySeasonalOverrides[]) => unknown, thisArg?: any): import("src/types").ReturnPolicySeasonalOverrides[];
                reduce(callbackfn: (previousValue: import("src/types").ReturnPolicySeasonalOverrides, currentValue: import("src/types").ReturnPolicySeasonalOverrides, currentIndex: number, array: import("src/types").ReturnPolicySeasonalOverrides[]) => import("src/types").ReturnPolicySeasonalOverrides): import("src/types").ReturnPolicySeasonalOverrides;
                reduce(callbackfn: (previousValue: import("src/types").ReturnPolicySeasonalOverrides, currentValue: import("src/types").ReturnPolicySeasonalOverrides, currentIndex: number, array: import("src/types").ReturnPolicySeasonalOverrides[]) => import("src/types").ReturnPolicySeasonalOverrides, initialValue: import("src/types").ReturnPolicySeasonalOverrides): import("src/types").ReturnPolicySeasonalOverrides;
                reduce<U_1>(callbackfn: (previousValue: U_1, currentValue: import("src/types").ReturnPolicySeasonalOverrides, currentIndex: number, array: import("src/types").ReturnPolicySeasonalOverrides[]) => U_1, initialValue: U_1): U_1;
                reduceRight(callbackfn: (previousValue: import("src/types").ReturnPolicySeasonalOverrides, currentValue: import("src/types").ReturnPolicySeasonalOverrides, currentIndex: number, array: import("src/types").ReturnPolicySeasonalOverrides[]) => import("src/types").ReturnPolicySeasonalOverrides): import("src/types").ReturnPolicySeasonalOverrides;
                reduceRight(callbackfn: (previousValue: import("src/types").ReturnPolicySeasonalOverrides, currentValue: import("src/types").ReturnPolicySeasonalOverrides, currentIndex: number, array: import("src/types").ReturnPolicySeasonalOverrides[]) => import("src/types").ReturnPolicySeasonalOverrides, initialValue: import("src/types").ReturnPolicySeasonalOverrides): import("src/types").ReturnPolicySeasonalOverrides;
                reduceRight<U_2>(callbackfn: (previousValue: U_2, currentValue: import("src/types").ReturnPolicySeasonalOverrides, currentIndex: number, array: import("src/types").ReturnPolicySeasonalOverrides[]) => U_2, initialValue: U_2): U_2;
                find<S_2 extends import("src/types").ReturnPolicySeasonalOverrides>(predicate: (value: import("src/types").ReturnPolicySeasonalOverrides, index: number, obj: import("src/types").ReturnPolicySeasonalOverrides[]) => value is S_2, thisArg?: any): S_2 | undefined;
                find(predicate: (value: import("src/types").ReturnPolicySeasonalOverrides, index: number, obj: import("src/types").ReturnPolicySeasonalOverrides[]) => unknown, thisArg?: any): import("src/types").ReturnPolicySeasonalOverrides | undefined;
                findIndex(predicate: (value: import("src/types").ReturnPolicySeasonalOverrides, index: number, obj: import("src/types").ReturnPolicySeasonalOverrides[]) => unknown, thisArg?: any): number;
                fill(value: import("src/types").ReturnPolicySeasonalOverrides, start?: number | undefined, end?: number | undefined): import("src/types").ReturnPolicySeasonalOverrides[];
                copyWithin(target: number, start?: number | undefined, end?: number | undefined): import("src/types").ReturnPolicySeasonalOverrides[];
                entries(): IterableIterator<[number, import("src/types").ReturnPolicySeasonalOverrides]>;
                keys(): IterableIterator<number>;
                values(): IterableIterator<import("src/types").ReturnPolicySeasonalOverrides>;
                includes(searchElement: import("src/types").ReturnPolicySeasonalOverrides, fromIndex?: number | undefined): boolean;
                flatMap<U_3, This = undefined>(callback: (this: This, value: import("src/types").ReturnPolicySeasonalOverrides, index: number, array: import("src/types").ReturnPolicySeasonalOverrides[]) => U_3 | readonly U_3[], thisArg?: This | undefined): U_3[];
                flat<A, D extends number = 1>(this: A, depth?: D | undefined): FlatArray<A, D>[];
                at(index: number): import("src/types").ReturnPolicySeasonalOverrides | undefined;
                findLast<S_3 extends import("src/types").ReturnPolicySeasonalOverrides>(predicate: (value: import("src/types").ReturnPolicySeasonalOverrides, index: number, array: import("src/types").ReturnPolicySeasonalOverrides[]) => value is S_3, thisArg?: any): S_3 | undefined;
                findLast(predicate: (value: import("src/types").ReturnPolicySeasonalOverrides, index: number, array: import("src/types").ReturnPolicySeasonalOverrides[]) => unknown, thisArg?: any): import("src/types").ReturnPolicySeasonalOverrides | undefined;
                findLastIndex(predicate: (value: import("src/types").ReturnPolicySeasonalOverrides, index: number, array: import("src/types").ReturnPolicySeasonalOverrides[]) => unknown, thisArg?: any): number;
                [Symbol.iterator](): IterableIterator<import("src/types").ReturnPolicySeasonalOverrides>;
                [Symbol.unscopables]: {
                    [x: number]: boolean | undefined;
                    length?: boolean | undefined;
                    toString?: boolean | undefined;
                    toLocaleString?: boolean | undefined;
                    pop?: boolean | undefined;
                    push?: boolean | undefined;
                    concat?: boolean | undefined;
                    join?: boolean | undefined;
                    reverse?: boolean | undefined;
                    shift?: boolean | undefined;
                    slice?: boolean | undefined;
                    sort?: boolean | undefined;
                    splice?: boolean | undefined;
                    unshift?: boolean | undefined;
                    indexOf?: boolean | undefined;
                    lastIndexOf?: boolean | undefined;
                    every?: boolean | undefined;
                    some?: boolean | undefined;
                    forEach?: boolean | undefined;
                    map?: boolean | undefined;
                    filter?: boolean | undefined;
                    reduce?: boolean | undefined;
                    reduceRight?: boolean | undefined;
                    find?: boolean | undefined;
                    findIndex?: boolean | undefined;
                    fill?: boolean | undefined;
                    copyWithin?: boolean | undefined;
                    entries?: boolean | undefined;
                    keys?: boolean | undefined;
                    values?: boolean | undefined;
                    includes?: boolean | undefined;
                    flatMap?: boolean | undefined;
                    flat?: boolean | undefined;
                    at?: boolean | undefined;
                    findLast?: boolean | undefined;
                    findLastIndex?: boolean | undefined;
                    [Symbol.iterator]?: boolean | undefined;
                    readonly [Symbol.unscopables]?: boolean | undefined;
                };
                '@type': string;
            } | undefined;
            returnPolicyCategory?: import("src/types").ReturnPolicyCategory | undefined;
            returnMethod?: import("src/types").ReturnMethod | import("src/types").ReturnMethod[] | undefined;
            returnFees?: import("src/types").ReturnFees | import("src/types").ReturnFees[] | undefined;
            refundType?: import("src/types").RefundType | import("src/types").RefundType[] | undefined;
            applicableCountry?: string | string[] | undefined;
            returnPolicyCountry?: string | string[] | undefined;
            merchantReturnLink?: string | undefined;
            itemCondition?: import("src/types").ItemCondition | import("src/types").ItemCondition[] | undefined;
            inStoreReturnsOffered?: boolean | undefined;
            restockingFee?: string | number | undefined;
            returnShippingFeesAmount?: {
                value: number;
                currency: string;
            } | undefined;
            customerRemorseReturnFees?: import("src/types").ReturnFees | import("src/types").ReturnFees[] | undefined;
            customerRemorseReturnLabelSource?: import("src/types").ReturnLabelSource | undefined;
            itemDefectReturnFees?: import("src/types").ReturnFees | import("src/types").ReturnFees[] | undefined;
            itemDefectReturnLabelSource?: import("src/types").ReturnLabelSource | undefined;
            '@type': string;
        } | undefined;
        seller: {
            '@type': string;
            name: string;
        };
        '@type': string;
        price: string;
        priceCurrency: string;
        priceValidUntil?: string | undefined;
        itemCondition?: string | undefined;
        availability?: string | undefined;
        url?: string | undefined;
        validFrom?: string | undefined;
    }[] | undefined;
} | undefined;
