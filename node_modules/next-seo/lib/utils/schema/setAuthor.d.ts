import { ArticleAuthor } from '../../types';
export declare function setAuthor(author?: string | string[] | ArticleAuthor | ArticleAuthor[]): {
    '@type': string;
    name: string;
    url?: undefined;
} | {
    '@type': "Person" | "Organization";
    name: string;
    url: string | undefined;
} | ({
    '@type': string;
    name: string;
    url?: undefined;
} | {
    '@type': "Person" | "Organization";
    name: string;
    url: string | undefined;
} | undefined)[] | undefined;
