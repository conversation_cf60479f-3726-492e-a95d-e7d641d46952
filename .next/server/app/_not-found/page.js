/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/_not-found/page";
exports.ids = ["app/_not-found/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Ffloripa-guide-v4%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Ffloripa-guide-v4&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Ffloripa-guide-v4%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Ffloripa-guide-v4&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n          children: [\"/_not-found\", {\n            children: ['__PAGE__', {}, {\n              page: [\n                () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)),\n                \"next/dist/client/components/not-found-error\"\n              ]\n            }]\n          }, {}]\n        },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/_not-found/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/_not-found/page\",\n        pathname: \"/_not-found\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Ffloripa-guide-v4%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Ffloripa-guide-v4&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Ffloripa-guide-v4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Ffloripa-guide-v4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Ffloripa-guide-v4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Ffloripa-guide-v4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Ffloripa-guide-v4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Ffloripa-guide-v4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Ffloripa-guide-v4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Ffloripa-guide-v4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Ffloripa-guide-v4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Ffloripa-guide-v4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Ffloripa-guide-v4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Ffloripa-guide-v4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Ffloripa-guide-v4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Ffloripa-guide-v4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Ffloripa-guide-v4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Ffloripa-guide-v4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Ffloripa-guide-v4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Ffloripa-guide-v4%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Ffloripa-guide-v4%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Montserrat%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-montserrat%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%2C%5C%22800%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22montserrat%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Ffloripa-guide-v4%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Open_Sans%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-open-sans%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22openSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Ffloripa-guide-v4%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Ffloripa-guide-v4%2Fsrc%2Fcomponents%2Fcommon%2FAdSense.tsx%22%2C%22ids%22%3A%5B%22AdSenseProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Ffloripa-guide-v4%2Fsrc%2Fcomponents%2Flayout%2FFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Ffloripa-guide-v4%2Fsrc%2Fcomponents%2Flayout%2FHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Ffloripa-guide-v4%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Montserrat%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-montserrat%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%2C%5C%22800%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22montserrat%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Ffloripa-guide-v4%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Open_Sans%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-open-sans%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22openSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Ffloripa-guide-v4%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Ffloripa-guide-v4%2Fsrc%2Fcomponents%2Fcommon%2FAdSense.tsx%22%2C%22ids%22%3A%5B%22AdSenseProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Ffloripa-guide-v4%2Fsrc%2Fcomponents%2Flayout%2FFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Ffloripa-guide-v4%2Fsrc%2Fcomponents%2Flayout%2FHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/common/AdSense.tsx */ \"(ssr)/./src/components/common/AdSense.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/Footer.tsx */ \"(ssr)/./src/components/layout/Footer.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/Header.tsx */ \"(ssr)/./src/components/layout/Header.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Ffloripa-guide-v4%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Montserrat%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-montserrat%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%2C%5C%22800%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22montserrat%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Ffloripa-guide-v4%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Open_Sans%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-open-sans%5C%22%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22openSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Ffloripa-guide-v4%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Ffloripa-guide-v4%2Fsrc%2Fcomponents%2Fcommon%2FAdSense.tsx%22%2C%22ids%22%3A%5B%22AdSenseProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Ffloripa-guide-v4%2Fsrc%2Fcomponents%2Flayout%2FFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Ffloripa-guide-v4%2Fsrc%2Fcomponents%2Flayout%2FHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/common/AdSense.tsx":
/*!*******************************************!*\
  !*** ./src/components/common/AdSense.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AdSenseProvider: () => (/* binding */ AdSenseProvider),\n/* harmony export */   InArticleAd: () => (/* binding */ InArticleAd),\n/* harmony export */   LeaderboardAd: () => (/* binding */ LeaderboardAd),\n/* harmony export */   MobileBannerAd: () => (/* binding */ MobileBannerAd),\n/* harmony export */   NativeAd: () => (/* binding */ NativeAd),\n/* harmony export */   RectangleAd: () => (/* binding */ RectangleAd),\n/* harmony export */   SidebarAd: () => (/* binding */ SidebarAd),\n/* harmony export */   \"default\": () => (/* binding */ AdSense),\n/* harmony export */   useAdSense: () => (/* binding */ useAdSense)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default,LeaderboardAd,RectangleAd,SidebarAd,MobileBannerAd,InArticleAd,NativeAd,useAdSense,AdSenseProvider auto */ \n\n\n// Configurações de tamanhos dos anúncios\nconst adFormats = {\n    auto: {\n        width: \"auto\",\n        height: \"auto\"\n    },\n    rectangle: {\n        width: 300,\n        height: 250\n    },\n    vertical: {\n        width: 160,\n        height: 600\n    },\n    horizontal: {\n        width: 728,\n        height: 90\n    }\n};\nfunction AdSense({ slot, format = \"auto\", responsive = true, className, style }) {\n    const adClient = process.env.NEXT_PUBLIC_ADSENSE_CLIENT_ID;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Verificar se o AdSense já foi carregado\n        if (false) {}\n    }, []);\n    // Não renderizar se não tiver client ID configurado\n    if (!adClient) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg p-4 text-center\", className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-500 text-sm\",\n                    children: \"AdSense n\\xe3o configurado\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/common/AdSense.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-xs text-gray-400 mt-1\",\n                    children: \"Configure NEXT_PUBLIC_ADSENSE_CLIENT_ID\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/common/AdSense.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/common/AdSense.tsx\",\n            lineNumber: 57,\n            columnNumber: 7\n        }, this);\n    }\n    const adStyle = {\n        display: \"block\",\n        ...adFormats[format],\n        ...style\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"ad-container\", className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ins\", {\n            className: \"adsbygoogle\",\n            style: adStyle,\n            \"data-ad-client\": adClient,\n            \"data-ad-slot\": slot,\n            \"data-ad-format\": responsive ? \"auto\" : format,\n            \"data-full-width-responsive\": responsive ? \"true\" : \"false\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/common/AdSense.tsx\",\n            lineNumber: 76,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/common/AdSense.tsx\",\n        lineNumber: 75,\n        columnNumber: 5\n    }, this);\n}\n// Componentes pré-configurados para diferentes posições\nfunction LeaderboardAd({ className }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AdSense, {\n        slot: \"1234567890\" // Substitua pelo slot real\n        ,\n        format: \"horizontal\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"my-8\", className)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/common/AdSense.tsx\",\n        lineNumber: 92,\n        columnNumber: 5\n    }, this);\n}\nfunction RectangleAd({ className }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AdSense, {\n        slot: \"1234567891\" // Substitua pelo slot real\n        ,\n        format: \"rectangle\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"my-6\", className)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/common/AdSense.tsx\",\n        lineNumber: 102,\n        columnNumber: 5\n    }, this);\n}\nfunction SidebarAd({ className }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AdSense, {\n        slot: \"1234567892\" // Substitua pelo slot real\n        ,\n        format: \"vertical\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"sticky top-4\", className)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/common/AdSense.tsx\",\n        lineNumber: 112,\n        columnNumber: 5\n    }, this);\n}\nfunction MobileBannerAd({ className }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"block md:hidden\", className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AdSense, {\n            slot: \"1234567893\" // Substitua pelo slot real\n            ,\n            format: \"auto\",\n            responsive: true\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/common/AdSense.tsx\",\n            lineNumber: 123,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/common/AdSense.tsx\",\n        lineNumber: 122,\n        columnNumber: 5\n    }, this);\n}\nfunction InArticleAd({ className }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"my-8 flex justify-center\", className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AdSense, {\n            slot: \"1234567894\" // Substitua pelo slot real\n            ,\n            format: \"rectangle\",\n            responsive: true\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/common/AdSense.tsx\",\n            lineNumber: 135,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/common/AdSense.tsx\",\n        lineNumber: 134,\n        columnNumber: 5\n    }, this);\n}\n// Componente para anúncios nativos\nfunction NativeAd({ className }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"my-6\", className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-xs text-gray-500 mb-2 text-center\",\n                children: \"Publicidade\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/common/AdSense.tsx\",\n                lineNumber: 148,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AdSense, {\n                slot: \"1234567895\" // Substitua pelo slot real\n                ,\n                format: \"auto\",\n                responsive: true\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/common/AdSense.tsx\",\n                lineNumber: 149,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/common/AdSense.tsx\",\n        lineNumber: 147,\n        columnNumber: 5\n    }, this);\n}\n// Hook para carregar o script do AdSense\nfunction useAdSense() {\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const adClient = process.env.NEXT_PUBLIC_ADSENSE_CLIENT_ID;\n        if (!adClient || \"undefined\" === \"undefined\") return;\n        // Verificar se o script já foi carregado\n        const existingScript = document.querySelector(`script[src*=\"pagead2.googlesyndication.com/pagead/js/adsbygoogle.js\"]`);\n        if (!existingScript) {\n            const script = document.createElement(\"script\");\n            script.async = true;\n            script.src = `https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=${adClient}`;\n            script.crossOrigin = \"anonymous\";\n            script.onerror = ()=>{\n                console.error(\"Erro ao carregar script do AdSense\");\n            };\n            document.head.appendChild(script);\n        }\n    }, []);\n}\n// Componente para carregar o AdSense globalmente\nfunction AdSenseProvider({ children }) {\n    useAdSense();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/common/AdSense.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Footer.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Footer.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Heart_Instagram_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Heart,Instagram,Mail,MapPin,Phone,Twitter,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/instagram.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Heart_Instagram_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Heart,Instagram,Mail,MapPin,Phone,Twitter,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/facebook.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Heart_Instagram_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Heart,Instagram,Mail,MapPin,Phone,Twitter,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/twitter.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Heart_Instagram_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Heart,Instagram,Mail,MapPin,Phone,Twitter,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/youtube.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Heart_Instagram_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Heart,Instagram,Mail,MapPin,Phone,Twitter,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Heart_Instagram_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Heart,Instagram,Mail,MapPin,Phone,Twitter,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Heart_Instagram_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Heart,Instagram,Mail,MapPin,Phone,Twitter,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Heart_Instagram_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Heart,Instagram,Mail,MapPin,Phone,Twitter,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/separator */ \"(ssr)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/constants */ \"(ssr)/./src/lib/constants.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nconst footerSections = [\n    {\n        title: \"Destinos\",\n        links: [\n            {\n                name: \"Praias do Norte\",\n                href: \"/praias?regiao=norte\"\n            },\n            {\n                name: \"Praias do Sul\",\n                href: \"/praias?regiao=sul\"\n            },\n            {\n                name: \"Praias do Leste\",\n                href: \"/praias?regiao=leste\"\n            },\n            {\n                name: \"Centro Hist\\xf3rico\",\n                href: \"/praias?regiao=centro\"\n            },\n            {\n                name: \"Lagoa da Concei\\xe7\\xe3o\",\n                href: \"/praias/lagoa-da-conceicao\"\n            },\n            {\n                name: \"Jurer\\xea Internacional\",\n                href: \"/praias/jurere-internacional\"\n            }\n        ]\n    },\n    {\n        title: \"Experi\\xeancias\",\n        links: [\n            {\n                name: \"Gastronomia UNESCO\",\n                href: \"/gastronomia\"\n            },\n            {\n                name: \"Rota das Ostras\",\n                href: \"/gastronomia/rota-das-ostras\"\n            },\n            {\n                name: \"Trilhas e Ecoturismo\",\n                href: \"/atividades/trilhas\"\n            },\n            {\n                name: \"Esportes Aqu\\xe1ticos\",\n                href: \"/atividades/esportes-aquaticos\"\n            },\n            {\n                name: \"Vida Noturna\",\n                href: \"/atividades/vida-noturna\"\n            },\n            {\n                name: \"Cultura A\\xe7oriana\",\n                href: \"/atividades/cultura\"\n            }\n        ]\n    },\n    {\n        title: \"Planejamento\",\n        links: [\n            {\n                name: \"Roteiros Prontos\",\n                href: \"/planejador/roteiros\"\n            },\n            {\n                name: \"Calculadora de Or\\xe7amento\",\n                href: \"/planejador/orcamento\"\n            },\n            {\n                name: \"Quando Visitar\",\n                href: \"/planejador/quando-visitar\"\n            },\n            {\n                name: \"Como Chegar\",\n                href: \"/solucoes/transporte\"\n            },\n            {\n                name: \"Onde Ficar\",\n                href: \"/hospedagem\"\n            },\n            {\n                name: \"Seguro Viagem\",\n                href: \"/seguro-viagem\"\n            }\n        ]\n    },\n    {\n        title: \"Recursos\",\n        links: [\n            {\n                name: \"Guia de Seguran\\xe7a\",\n                href: \"/solucoes/seguranca\"\n            },\n            {\n                name: \"Transporte Local\",\n                href: \"/solucoes/transporte\"\n            },\n            {\n                name: \"Clima e Condi\\xe7\\xf5es\",\n                href: \"/clima\"\n            },\n            {\n                name: \"Eventos 2025\",\n                href: \"/eventos\"\n            },\n            {\n                name: \"Blog\",\n                href: \"/blog\"\n            },\n            {\n                name: \"Contato\",\n                href: \"/contato\"\n            }\n        ]\n    }\n];\nconst socialLinks = [\n    {\n        name: \"Instagram\",\n        href: _lib_constants__WEBPACK_IMPORTED_MODULE_6__.SOCIAL_LINKS.INSTAGRAM,\n        icon: _barrel_optimize_names_Facebook_Heart_Instagram_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    },\n    {\n        name: \"Facebook\",\n        href: _lib_constants__WEBPACK_IMPORTED_MODULE_6__.SOCIAL_LINKS.FACEBOOK,\n        icon: _barrel_optimize_names_Facebook_Heart_Instagram_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n    },\n    {\n        name: \"Twitter\",\n        href: _lib_constants__WEBPACK_IMPORTED_MODULE_6__.SOCIAL_LINKS.TWITTER,\n        icon: _barrel_optimize_names_Facebook_Heart_Instagram_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n    },\n    {\n        name: \"YouTube\",\n        href: _lib_constants__WEBPACK_IMPORTED_MODULE_6__.SOCIAL_LINKS.YOUTUBE,\n        icon: _barrel_optimize_names_Facebook_Heart_Instagram_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n    }\n];\nfunction Footer() {\n    const currentYear = new Date().getFullYear();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-gray-900 text-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-primary-500\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-custom py-12\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-4xl mx-auto text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl font-bold mb-4\",\n                                children: \"Receba as melhores dicas de Florian\\xf3polis\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Footer.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-primary-100 mb-8 max-w-2xl mx-auto\",\n                                children: \"Cadastre-se na nossa newsletter e receba roteiros exclusivos, dicas de locais, ofertas especiais e muito mais!\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Footer.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4 max-w-md mx-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        type: \"email\",\n                                        placeholder: \"Seu melhor e-mail\",\n                                        className: \"bg-white/10 border-white/20 text-white placeholder:text-white/70 focus:border-white\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Footer.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"sand\",\n                                        size: \"lg\",\n                                        className: \"whitespace-nowrap\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Heart_Instagram_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Footer.tsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Inscrever-se\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Footer.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Footer.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-primary-100 mt-4\",\n                                children: \"Sem spam. Cancele quando quiser. \\uD83D\\uDCE7\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Footer.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Footer.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Footer.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Footer.tsx\",\n                lineNumber: 82,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container-custom py-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: \"/\",\n                                    className: \"flex items-center space-x-3 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white font-bold text-xl\",\n                                                children: \"FG\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Footer.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Footer.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-heading font-bold text-xl\",\n                                                    children: \"Floripa Guide\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Footer.tsx\",\n                                                    lineNumber: 122,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-400 text-sm\",\n                                                    children: \"Ilha da Magia\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Footer.tsx\",\n                                                    lineNumber: 123,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Footer.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Footer.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 mb-6 leading-relaxed\",\n                                    children: \"Seu guia completo para descobrir as 42 praias paradis\\xedacas, gastronomia UNESCO e aventuras inesquec\\xedveis de Florian\\xf3polis.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Footer.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 text-sm text-gray-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Heart_Instagram_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-4 w-4 text-primary-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Footer.tsx\",\n                                                    lineNumber: 135,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Florian\\xf3polis, Santa Catarina\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Footer.tsx\",\n                                                    lineNumber: 136,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Footer.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 text-sm text-gray-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Heart_Instagram_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"h-4 w-4 text-primary-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Footer.tsx\",\n                                                    lineNumber: 139,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: _lib_constants__WEBPACK_IMPORTED_MODULE_6__.APP_CONFIG.phone\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Footer.tsx\",\n                                                    lineNumber: 140,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Footer.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 text-sm text-gray-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Heart_Instagram_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-4 w-4 text-primary-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Footer.tsx\",\n                                                    lineNumber: 143,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: _lib_constants__WEBPACK_IMPORTED_MODULE_6__.APP_CONFIG.email\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Footer.tsx\",\n                                                    lineNumber: 144,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Footer.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Footer.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-4\",\n                                    children: socialLinks.map((social)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: social.href,\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-primary-500 transition-colors group\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(social.icon, {\n                                                className: \"h-5 w-5 text-gray-400 group-hover:text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Footer.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, social.name, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Footer.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Footer.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Footer.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 11\n                        }, this),\n                        footerSections.map((section)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold text-white mb-4\",\n                                        children: section.title\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Footer.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-3\",\n                                        children: section.links.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    href: link.href,\n                                                    className: \"text-gray-300 hover:text-primary-400 transition-colors text-sm\",\n                                                    children: link.name\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Footer.tsx\",\n                                                    lineNumber: 171,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, link.name, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Footer.tsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Footer.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, section.title, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Footer.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 13\n                            }, this))\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Footer.tsx\",\n                    lineNumber: 114,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Footer.tsx\",\n                lineNumber: 113,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_5__.Separator, {\n                className: \"bg-gray-800\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Footer.tsx\",\n                lineNumber: 185,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container-custom py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row items-center space-y-2 md:space-y-0 md:space-x-6 text-sm text-gray-400\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            \"\\xa9 \",\n                                            currentYear,\n                                            \" \",\n                                            _lib_constants__WEBPACK_IMPORTED_MODULE_6__.APP_CONFIG.name,\n                                            \". Todos os direitos reservados.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Footer.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                href: \"/privacidade\",\n                                                className: \"hover:text-primary-400 transition-colors\",\n                                                children: \"Privacidade\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Footer.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                href: \"/termos\",\n                                                className: \"hover:text-primary-400 transition-colors\",\n                                                children: \"Termos\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Footer.tsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                href: \"/cookies\",\n                                                className: \"hover:text-primary-400 transition-colors\",\n                                                children: \"Cookies\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Footer.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Footer.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Footer.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 text-sm text-gray-400\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Feito com\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Footer.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Heart_Instagram_Mail_MapPin_Phone_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-4 w-4 text-red-500\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Footer.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"para os amantes da Ilha da Magia\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Footer.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg\",\n                                        children: \"\\uD83C\\uDFDD️\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Footer.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Footer.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Footer.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6 pt-6 border-t border-gray-800\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-500 text-center max-w-4xl mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"Aviso:\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Footer.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 13\n                                }, this),\n                                \" Este site cont\\xe9m links de afiliados. Quando voc\\xea clica em um link e faz uma compra, podemos receber uma pequena comiss\\xe3o sem custo adicional para voc\\xea. Isso nos ajuda a manter o site funcionando e continuar fornecendo conte\\xfado gratuito e de qualidade.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Footer.tsx\",\n                            lineNumber: 217,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Footer.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Footer.tsx\",\n                lineNumber: 188,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Footer.tsx\",\n        lineNumber: 80,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Footer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_MapPin_Menu_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,MapPin,Menu,Search,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_MapPin_Menu_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,MapPin,Menu,Search,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_MapPin_Menu_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,MapPin,Menu,Search,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_MapPin_Menu_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,MapPin,Menu,Search,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Heart_MapPin_Menu_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Heart,MapPin,Menu,Search,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./src/components/ui/input.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nconst navigation = [\n    {\n        name: \"Praias\",\n        href: \"/praias\",\n        icon: \"\\uD83C\\uDFD6️\"\n    },\n    {\n        name: \"Gastronomia\",\n        href: \"/gastronomia\",\n        icon: \"\\uD83C\\uDF7D️\"\n    },\n    {\n        name: \"Atividades\",\n        href: \"/atividades\",\n        icon: \"\\uD83C\\uDFC4‍♂️\"\n    },\n    {\n        name: \"Solu\\xe7\\xf5es\",\n        href: \"/solucoes\",\n        icon: \"\\uD83D\\uDEE1️\"\n    },\n    {\n        name: \"Planejador\",\n        href: \"/planejador\",\n        icon: \"\\uD83D\\uDDFA️\"\n    }\n];\nfunction Header() {\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSearchOpen, setIsSearchOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isScrolled, setIsScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    // Detectar scroll para mudar estilo do header\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleScroll = ()=>{\n            setIsScrolled(window.scrollY > 10);\n        };\n        window.addEventListener(\"scroll\", handleScroll);\n        return ()=>window.removeEventListener(\"scroll\", handleScroll);\n    }, []);\n    // Fechar menu mobile quando mudar de rota\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setIsMenuOpen(false);\n        setIsSearchOpen(false);\n    }, [\n        pathname\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"fixed top-0 left-0 right-0 z-50 transition-all duration-300\", isScrolled ? \"bg-white/95 backdrop-blur-md shadow-md\" : \"bg-transparent\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container-custom\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between h-16 lg:h-20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/\",\n                                className: \"flex items-center space-x-2 group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-lg flex items-center justify-center group-hover:scale-105 transition-transform\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white font-bold text-lg\",\n                                            children: \"FG\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Header.tsx\",\n                                            lineNumber: 56,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Header.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hidden sm:block\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"font-heading font-bold text-xl transition-colors\", isScrolled ? \"text-gray-900\" : \"text-white\"),\n                                                children: \"Floripa Guide\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Header.tsx\",\n                                                lineNumber: 59,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"text-xs transition-colors\", isScrolled ? \"text-gray-600\" : \"text-white/80\"),\n                                                children: \"Ilha da Magia\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Header.tsx\",\n                                                lineNumber: 65,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Header.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Header.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"hidden lg:flex items-center space-x-1\",\n                                children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: item.href,\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 hover:scale-105\", pathname === item.href ? \"bg-primary-500 text-white shadow-md\" : isScrolled ? \"text-gray-700 hover:bg-gray-100\" : \"text-white/90 hover:bg-white/10\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"mr-2\",\n                                                children: item.icon\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Header.tsx\",\n                                                lineNumber: 89,\n                                                columnNumber: 17\n                                            }, this),\n                                            item.name\n                                        ]\n                                    }, item.name, true, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Header.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Header.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden lg:flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"icon\",\n                                        onClick: ()=>setIsSearchOpen(!isSearchOpen),\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"transition-colors\", isScrolled ? \"text-gray-700 hover:bg-gray-100\" : \"text-white hover:bg-white/10\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_MapPin_Menu_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Header.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Header.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"icon\",\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"transition-colors\", isScrolled ? \"text-gray-700 hover:bg-gray-100\" : \"text-white hover:bg-white/10\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_MapPin_Menu_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Header.tsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Header.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"ocean\",\n                                        size: \"sm\",\n                                        className: \"shadow-md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_MapPin_Menu_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Header.tsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Planejar Viagem\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Header.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Header.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:hidden flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"icon\",\n                                        onClick: ()=>setIsSearchOpen(!isSearchOpen),\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"transition-colors\", isScrolled ? \"text-gray-700 hover:bg-gray-100\" : \"text-white hover:bg-white/10\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_MapPin_Menu_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Header.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Header.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"icon\",\n                                        onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"transition-colors\", isScrolled ? \"text-gray-700 hover:bg-gray-100\" : \"text-white hover:bg-white/10\"),\n                                        children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_MapPin_Menu_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-6 w-6\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Header.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_MapPin_Menu_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-6 w-6\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Header.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Header.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Header.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Header.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 9\n                    }, this),\n                    isSearchOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"pb-4 animate-slide-up\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_MapPin_Menu_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Header.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                    type: \"text\",\n                                    placeholder: \"Buscar praias, restaurantes, atividades...\",\n                                    className: \"pl-10 pr-4 py-3 w-full bg-white/95 backdrop-blur-sm border-gray-200 focus:border-primary-500\",\n                                    autoFocus: true\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Header.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Header.tsx\",\n                            lineNumber: 172,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Header.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Header.tsx\",\n                lineNumber: 51,\n                columnNumber: 7\n            }, this),\n            isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:hidden bg-white border-t border-gray-200 shadow-lg animate-slide-up\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container-custom py-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"space-y-2\",\n                            children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: item.href,\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"flex items-center px-4 py-3 rounded-lg text-sm font-medium transition-colors\", pathname === item.href ? \"bg-primary-50 text-primary-700 border-l-4 border-primary-500\" : \"text-gray-700 hover:bg-gray-50\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"mr-3 text-lg\",\n                                            children: item.icon\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Header.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 19\n                                        }, this),\n                                        item.name\n                                    ]\n                                }, item.name, true, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Header.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Header.tsx\",\n                            lineNumber: 189,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 pt-6 border-t border-gray-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_MapPin_Menu_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Header.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Favoritos\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Header.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"ocean\",\n                                        size: \"sm\",\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Heart_MapPin_Menu_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Header.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Planejar\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Header.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Header.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Header.tsx\",\n                            lineNumber: 207,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Header.tsx\",\n                    lineNumber: 188,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Header.tsx\",\n                lineNumber: 187,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Header.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\",\n            // Variantes customizadas para Floripa Guide\n            ocean: \"bg-primary-500 text-white hover:bg-primary-600 shadow-md hover:shadow-lg transition-all duration-200\",\n            nature: \"bg-secondary-500 text-white hover:bg-secondary-600 shadow-md hover:shadow-lg transition-all duration-200\",\n            sand: \"bg-accent-500 text-gray-900 hover:bg-accent-600 shadow-md hover:shadow-lg transition-all duration-200\",\n            gradient: \"bg-gradient-to-r from-primary-500 to-secondary-500 text-white hover:from-primary-600 hover:to-secondary-600 shadow-md hover:shadow-lg transition-all duration-200\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            xl: \"h-12 rounded-md px-10 text-base\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/ui/button.tsx\",\n        lineNumber: 52,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/input.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/input.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, type, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/ui/input.tsx\",\n        lineNumber: 11,\n        columnNumber: 7\n    }, undefined);\n});\nInput.displayName = \"Input\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9pbnB1dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUE4QjtBQUVFO0FBS2hDLE1BQU1FLHNCQUFRRiw2Q0FBZ0IsQ0FDNUIsQ0FBQyxFQUFFSSxTQUFTLEVBQUVDLElBQUksRUFBRSxHQUFHQyxPQUFPLEVBQUVDO0lBQzlCLHFCQUNFLDhEQUFDQztRQUNDSCxNQUFNQTtRQUNORCxXQUFXSCw4Q0FBRUEsQ0FDWCxnV0FDQUc7UUFFRkcsS0FBS0E7UUFDSixHQUFHRCxLQUFLOzs7Ozs7QUFHZjtBQUVGSixNQUFNTyxXQUFXLEdBQUc7QUFFSiIsInNvdXJjZXMiOlsid2VicGFjazovL2Zsb3JpcGEtZ3VpZGUtdjQvLi9zcmMvY29tcG9uZW50cy91aS9pbnB1dC50c3g/Yzk4MyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIlxuXG5pbXBvcnQgeyBjbiB9IGZyb20gXCJAL2xpYi91dGlsc1wiXG5cbmV4cG9ydCBpbnRlcmZhY2UgSW5wdXRQcm9wc1xuICBleHRlbmRzIFJlYWN0LklucHV0SFRNTEF0dHJpYnV0ZXM8SFRNTElucHV0RWxlbWVudD4ge31cblxuY29uc3QgSW5wdXQgPSBSZWFjdC5mb3J3YXJkUmVmPEhUTUxJbnB1dEVsZW1lbnQsIElucHV0UHJvcHM+KFxuICAoeyBjbGFzc05hbWUsIHR5cGUsIC4uLnByb3BzIH0sIHJlZikgPT4ge1xuICAgIHJldHVybiAoXG4gICAgICA8aW5wdXRcbiAgICAgICAgdHlwZT17dHlwZX1cbiAgICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICBcImZsZXggaC0xMCB3LWZ1bGwgcm91bmRlZC1tZCBib3JkZXIgYm9yZGVyLWlucHV0IGJnLWJhY2tncm91bmQgcHgtMyBweS0yIHRleHQtc20gcmluZy1vZmZzZXQtYmFja2dyb3VuZCBmaWxlOmJvcmRlci0wIGZpbGU6YmctdHJhbnNwYXJlbnQgZmlsZTp0ZXh0LXNtIGZpbGU6Zm9udC1tZWRpdW0gcGxhY2Vob2xkZXI6dGV4dC1tdXRlZC1mb3JlZ3JvdW5kIGZvY3VzLXZpc2libGU6b3V0bGluZS1ub25lIGZvY3VzLXZpc2libGU6cmluZy0yIGZvY3VzLXZpc2libGU6cmluZy1yaW5nIGZvY3VzLXZpc2libGU6cmluZy1vZmZzZXQtMiBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWQgZGlzYWJsZWQ6b3BhY2l0eS01MFwiLFxuICAgICAgICAgIGNsYXNzTmFtZVxuICAgICAgICApfVxuICAgICAgICByZWY9e3JlZn1cbiAgICAgICAgey4uLnByb3BzfVxuICAgICAgLz5cbiAgICApXG4gIH1cbilcbklucHV0LmRpc3BsYXlOYW1lID0gXCJJbnB1dFwiXG5cbmV4cG9ydCB7IElucHV0IH1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsImNuIiwiSW5wdXQiLCJmb3J3YXJkUmVmIiwiY2xhc3NOYW1lIiwidHlwZSIsInByb3BzIiwicmVmIiwiaW5wdXQiLCJkaXNwbGF5TmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/input.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/separator.tsx":
/*!*****************************************!*\
  !*** ./src/components/ui/separator.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Separator: () => (/* binding */ Separator)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_separator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-separator */ \"(ssr)/./node_modules/@radix-ui/react-separator/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Separator auto */ \n\n\n\nconst Separator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, orientation = \"horizontal\", decorative = true, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_separator__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        ref: ref,\n        decorative: decorative,\n        orientation: orientation,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"shrink-0 bg-border\", orientation === \"horizontal\" ? \"h-[1px] w-full\" : \"h-full w-[1px]\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/ui/separator.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, undefined));\nSeparator.displayName = _radix_ui_react_separator__WEBPACK_IMPORTED_MODULE_3__.Root.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9zZXBhcmF0b3IudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBRThCO0FBQ2lDO0FBRS9CO0FBRWhDLE1BQU1HLDBCQUFZSCw2Q0FBZ0IsQ0FJaEMsQ0FDRSxFQUFFSyxTQUFTLEVBQUVDLGNBQWMsWUFBWSxFQUFFQyxhQUFhLElBQUksRUFBRSxHQUFHQyxPQUFPLEVBQ3RFQyxvQkFFQSw4REFBQ1IsMkRBQXVCO1FBQ3RCUSxLQUFLQTtRQUNMRixZQUFZQTtRQUNaRCxhQUFhQTtRQUNiRCxXQUFXSCw4Q0FBRUEsQ0FDWCxzQkFDQUksZ0JBQWdCLGVBQWUsbUJBQW1CLGtCQUNsREQ7UUFFRCxHQUFHRyxLQUFLOzs7Ozs7QUFJZkwsVUFBVVEsV0FBVyxHQUFHViwyREFBdUIsQ0FBQ1UsV0FBVztBQUV2QyIsInNvdXJjZXMiOlsid2VicGFjazovL2Zsb3JpcGEtZ3VpZGUtdjQvLi9zcmMvY29tcG9uZW50cy91aS9zZXBhcmF0b3IudHN4Pzg0YzgiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCJcblxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCJcbmltcG9ydCAqIGFzIFNlcGFyYXRvclByaW1pdGl2ZSBmcm9tIFwiQHJhZGl4LXVpL3JlYWN0LXNlcGFyYXRvclwiXG5cbmltcG9ydCB7IGNuIH0gZnJvbSBcIkAvbGliL3V0aWxzXCJcblxuY29uc3QgU2VwYXJhdG9yID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgUmVhY3QuRWxlbWVudFJlZjx0eXBlb2YgU2VwYXJhdG9yUHJpbWl0aXZlLlJvb3Q+LFxuICBSZWFjdC5Db21wb25lbnRQcm9wc1dpdGhvdXRSZWY8dHlwZW9mIFNlcGFyYXRvclByaW1pdGl2ZS5Sb290PlxuPihcbiAgKFxuICAgIHsgY2xhc3NOYW1lLCBvcmllbnRhdGlvbiA9IFwiaG9yaXpvbnRhbFwiLCBkZWNvcmF0aXZlID0gdHJ1ZSwgLi4ucHJvcHMgfSxcbiAgICByZWZcbiAgKSA9PiAoXG4gICAgPFNlcGFyYXRvclByaW1pdGl2ZS5Sb290XG4gICAgICByZWY9e3JlZn1cbiAgICAgIGRlY29yYXRpdmU9e2RlY29yYXRpdmV9XG4gICAgICBvcmllbnRhdGlvbj17b3JpZW50YXRpb259XG4gICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICBcInNocmluay0wIGJnLWJvcmRlclwiLFxuICAgICAgICBvcmllbnRhdGlvbiA9PT0gXCJob3Jpem9udGFsXCIgPyBcImgtWzFweF0gdy1mdWxsXCIgOiBcImgtZnVsbCB3LVsxcHhdXCIsXG4gICAgICAgIGNsYXNzTmFtZVxuICAgICAgKX1cbiAgICAgIHsuLi5wcm9wc31cbiAgICAvPlxuICApXG4pXG5TZXBhcmF0b3IuZGlzcGxheU5hbWUgPSBTZXBhcmF0b3JQcmltaXRpdmUuUm9vdC5kaXNwbGF5TmFtZVxuXG5leHBvcnQgeyBTZXBhcmF0b3IgfVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiU2VwYXJhdG9yUHJpbWl0aXZlIiwiY24iLCJTZXBhcmF0b3IiLCJmb3J3YXJkUmVmIiwiY2xhc3NOYW1lIiwib3JpZW50YXRpb24iLCJkZWNvcmF0aXZlIiwicHJvcHMiLCJyZWYiLCJSb290IiwiZGlzcGxheU5hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/separator.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/constants.ts":
/*!******************************!*\
  !*** ./src/lib/constants.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ACTIVITY_CATEGORIES: () => (/* binding */ ACTIVITY_CATEGORIES),\n/* harmony export */   ADSENSE_CONFIG: () => (/* binding */ ADSENSE_CONFIG),\n/* harmony export */   AFFILIATE_LINKS: () => (/* binding */ AFFILIATE_LINKS),\n/* harmony export */   ANALYTICS_CONFIG: () => (/* binding */ ANALYTICS_CONFIG),\n/* harmony export */   API_CONFIG: () => (/* binding */ API_CONFIG),\n/* harmony export */   APP_CONFIG: () => (/* binding */ APP_CONFIG),\n/* harmony export */   BEACH_TYPES: () => (/* binding */ BEACH_TYPES),\n/* harmony export */   BREAKPOINTS: () => (/* binding */ BREAKPOINTS),\n/* harmony export */   CACHE_DURATIONS: () => (/* binding */ CACHE_DURATIONS),\n/* harmony export */   CUISINE_TYPES: () => (/* binding */ CUISINE_TYPES),\n/* harmony export */   DIFFICULTY_LEVELS: () => (/* binding */ DIFFICULTY_LEVELS),\n/* harmony export */   ERROR_MESSAGES: () => (/* binding */ ERROR_MESSAGES),\n/* harmony export */   FLORIPA_COORDINATES: () => (/* binding */ FLORIPA_COORDINATES),\n/* harmony export */   IMAGE_CONFIG: () => (/* binding */ IMAGE_CONFIG),\n/* harmony export */   MONTHS: () => (/* binding */ MONTHS),\n/* harmony export */   PAGINATION_LIMITS: () => (/* binding */ PAGINATION_LIMITS),\n/* harmony export */   PRICE_RANGES: () => (/* binding */ PRICE_RANGES),\n/* harmony export */   PWA_CONFIG: () => (/* binding */ PWA_CONFIG),\n/* harmony export */   REGIONS: () => (/* binding */ REGIONS),\n/* harmony export */   SEASONS: () => (/* binding */ SEASONS),\n/* harmony export */   SEASON_MONTHS: () => (/* binding */ SEASON_MONTHS),\n/* harmony export */   SEA_CONDITIONS: () => (/* binding */ SEA_CONDITIONS),\n/* harmony export */   SEO_CONFIG: () => (/* binding */ SEO_CONFIG),\n/* harmony export */   SOCIAL_LINKS: () => (/* binding */ SOCIAL_LINKS),\n/* harmony export */   STORAGE_KEYS: () => (/* binding */ STORAGE_KEYS),\n/* harmony export */   SUCCESS_MESSAGES: () => (/* binding */ SUCCESS_MESSAGES),\n/* harmony export */   WEATHER_CONDITIONS: () => (/* binding */ WEATHER_CONDITIONS)\n/* harmony export */ });\n// Configurações gerais da aplicação\nconst APP_CONFIG = {\n    name: \"Floripa Guide\",\n    description: \"Seu guia completo de Florian\\xf3polis\",\n    url: \"https://floripaguide.com\",\n    version: \"1.0.0\",\n    author: \"Floripa Guide Team\",\n    email: \"<EMAIL>\",\n    phone: \"+55 48 99999-9999\",\n    whatsapp: \"+5548999999999\"\n};\n// Coordenadas de Florianópolis\nconst FLORIPA_COORDINATES = {\n    lat: -27.5954,\n    lng: -48.5480,\n    zoom: 11\n};\n// Regiões de Florianópolis\nconst REGIONS = {\n    NORTE: \"Norte\",\n    SUL: \"Sul\",\n    LESTE: \"Leste\",\n    CENTRO: \"Centro\"\n};\n// Tipos de praias\nconst BEACH_TYPES = {\n    SURF: \"Surf\",\n    FAMILIA: \"Fam\\xedlia\",\n    DESERTA: \"Deserta\",\n    BADALADA: \"Badalada\",\n    NATURISTA: \"Naturista\",\n    AVENTURA: \"Aventura\"\n};\n// Níveis de dificuldade\nconst DIFFICULTY_LEVELS = {\n    FACIL: \"F\\xe1cil\",\n    MODERADO: \"Moderado\",\n    DIFICIL: \"Dif\\xedcil\",\n    EXTREMO: \"Extremo\"\n};\n// Faixas de preço\nconst PRICE_RANGES = {\n    ECONOMICO: \"Econ\\xf4mico\",\n    MEDIO: \"M\\xe9dio\",\n    ALTO: \"Alto\",\n    PREMIUM: \"Premium\"\n};\n// Tipos de culinária\nconst CUISINE_TYPES = {\n    FRUTOS_DO_MAR: \"Frutos do Mar\",\n    BRASILEIRA: \"Brasileira\",\n    ITALIANA: \"Italiana\",\n    JAPONESA: \"Japonesa\",\n    VEGETARIANA: \"Vegetariana\",\n    INTERNACIONAL: \"Internacional\",\n    ACORIANA: \"A\\xe7oriana\",\n    CONTEMPORANEA: \"Contempor\\xe2nea\"\n};\n// Categorias de atividades\nconst ACTIVITY_CATEGORIES = {\n    ESPORTES_AQUATICOS: \"Esportes Aqu\\xe1ticos\",\n    TRILHAS: \"Trilhas\",\n    VIDA_NOTURNA: \"Vida Noturna\",\n    CULTURA: \"Cultura\",\n    AVENTURA: \"Aventura\",\n    GASTRONOMIA: \"Gastronomia\",\n    COMPRAS: \"Compras\",\n    RELAXAMENTO: \"Relaxamento\"\n};\n// Condições climáticas\nconst WEATHER_CONDITIONS = {\n    ENSOLARADO: \"Ensolarado\",\n    PARCIALMENTE_NUBLADO: \"Parcialmente Nublado\",\n    NUBLADO: \"Nublado\",\n    CHUVOSO: \"Chuvoso\",\n    TEMPESTADE: \"Tempestade\"\n};\n// Condições do mar\nconst SEA_CONDITIONS = {\n    CALMO: \"Calmo\",\n    MODERADO: \"Moderado\",\n    AGITADO: \"Agitado\",\n    MUITO_AGITADO: \"Muito Agitado\"\n};\n// Meses do ano\nconst MONTHS = {\n    JANEIRO: \"Janeiro\",\n    FEVEREIRO: \"Fevereiro\",\n    MARCO: \"Mar\\xe7o\",\n    ABRIL: \"Abril\",\n    MAIO: \"Maio\",\n    JUNHO: \"Junho\",\n    JULHO: \"Julho\",\n    AGOSTO: \"Agosto\",\n    SETEMBRO: \"Setembro\",\n    OUTUBRO: \"Outubro\",\n    NOVEMBRO: \"Novembro\",\n    DEZEMBRO: \"Dezembro\"\n};\n// Temporadas turísticas\nconst SEASONS = {\n    ALTA: \"Alta Temporada\",\n    MEDIA: \"M\\xe9dia Temporada\",\n    BAIXA: \"Baixa Temporada\"\n};\n// Meses por temporada\nconst SEASON_MONTHS = {\n    ALTA: [\n        \"DEZEMBRO\",\n        \"JANEIRO\",\n        \"FEVEREIRO\",\n        \"MARCO\"\n    ],\n    MEDIA: [\n        \"ABRIL\",\n        \"MAIO\",\n        \"SETEMBRO\",\n        \"OUTUBRO\",\n        \"NOVEMBRO\"\n    ],\n    BAIXA: [\n        \"JUNHO\",\n        \"JULHO\",\n        \"AGOSTO\"\n    ]\n};\n// URLs das redes sociais\nconst SOCIAL_LINKS = {\n    INSTAGRAM: \"https://instagram.com/floripaguide\",\n    FACEBOOK: \"https://facebook.com/floripaguide\",\n    TWITTER: \"https://twitter.com/floripaguide\",\n    YOUTUBE: \"https://youtube.com/@floripaguide\",\n    TIKTOK: \"https://tiktok.com/@floripaguide\"\n};\n// Links de afiliados\nconst AFFILIATE_LINKS = {\n    BOOKING: \"https://booking.com/affiliate/floripaguide\",\n    GETYOURGUIDE: \"https://getyourguide.com/affiliate/floripaguide\",\n    RENTCARS: \"https://rentcars.com/affiliate/floripaguide\",\n    SEGUROS_PROMO: \"https://segurospromo.com/affiliate/floripaguide\",\n    AIRBNB: \"https://airbnb.com/affiliate/floripaguide\"\n};\n// Configurações de SEO\nconst SEO_CONFIG = {\n    defaultTitle: \"Floripa Guide - Seu Guia Completo de Florian\\xf3polis\",\n    titleTemplate: \"%s | Floripa Guide\",\n    defaultDescription: \"Descubra as 42 praias paradis\\xedacas de Florian\\xf3polis, gastronomia UNESCO, aventuras inesquec\\xedveis e dicas exclusivas para sua viagem perfeita \\xe0 Ilha da Magia.\",\n    siteUrl: \"https://floripaguide.com\",\n    defaultImage: \"/images/og-default.jpg\",\n    twitterHandle: \"@floripaguide\"\n};\n// Configurações de analytics\nconst ANALYTICS_CONFIG = {\n    googleAnalyticsId: process.env.NEXT_PUBLIC_GA_ID || \"\",\n    googleAdsenseId: process.env.NEXT_PUBLIC_ADSENSE_CLIENT_ID || \"\",\n    hotjarId: process.env.NEXT_PUBLIC_HOTJAR_ID || \"\"\n};\n// Configurações de APIs externas\nconst API_CONFIG = {\n    googleMapsApiKey: process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY || \"\",\n    openWeatherApiKey: process.env.OPENWEATHER_API_KEY || \"\",\n    instagramApiKey: process.env.INSTAGRAM_API_KEY || \"\"\n};\n// Limites de paginação\nconst PAGINATION_LIMITS = {\n    BEACHES: 12,\n    RESTAURANTS: 9,\n    ACTIVITIES: 8,\n    SEARCH_RESULTS: 10\n};\n// Configurações de cache (em segundos)\nconst CACHE_DURATIONS = {\n    STATIC_DATA: 60 * 60 * 24,\n    WEATHER_DATA: 60 * 30,\n    SEARCH_RESULTS: 60 * 15,\n    USER_PREFERENCES: 60 * 60 * 24 * 7\n};\n// Breakpoints responsivos\nconst BREAKPOINTS = {\n    xs: 475,\n    sm: 640,\n    md: 768,\n    lg: 1024,\n    xl: 1280,\n    \"2xl\": 1536,\n    \"3xl\": 1600\n};\n// Configurações de imagens\nconst IMAGE_CONFIG = {\n    quality: 85,\n    formats: [\n        \"image/webp\",\n        \"image/avif\"\n    ],\n    sizes: {\n        thumbnail: 150,\n        small: 300,\n        medium: 600,\n        large: 1200,\n        xlarge: 1920\n    }\n};\n// Configurações de AdSense\nconst ADSENSE_CONFIG = {\n    slots: {\n        leaderboard: \"ca-pub-XXXXXXXX/XXXXXXXX\",\n        rectangle: \"ca-pub-XXXXXXXX/XXXXXXXX\",\n        sidebar: \"ca-pub-XXXXXXXX/XXXXXXXX\",\n        mobile_banner: \"ca-pub-XXXXXXXX/XXXXXXXX\",\n        in_article: \"ca-pub-XXXXXXXX/XXXXXXXX\"\n    },\n    sizes: {\n        leaderboard: [\n            728,\n            90\n        ],\n        rectangle: [\n            300,\n            250\n        ],\n        sidebar: [\n            300,\n            600\n        ],\n        mobile_banner: [\n            320,\n            50\n        ],\n        responsive: \"auto\"\n    }\n};\n// Mensagens de erro padrão\nconst ERROR_MESSAGES = {\n    GENERIC: \"Ops! Algo deu errado. Tente novamente.\",\n    NETWORK: \"Erro de conex\\xe3o. Verifique sua internet.\",\n    NOT_FOUND: \"Conte\\xfado n\\xe3o encontrado.\",\n    VALIDATION: \"Por favor, verifique os dados informados.\",\n    UNAUTHORIZED: \"Acesso n\\xe3o autorizado.\",\n    SERVER_ERROR: \"Erro interno do servidor.\"\n};\n// Mensagens de sucesso\nconst SUCCESS_MESSAGES = {\n    SAVED: \"Salvo com sucesso!\",\n    UPDATED: \"Atualizado com sucesso!\",\n    DELETED: \"Removido com sucesso!\",\n    SENT: \"Enviado com sucesso!\",\n    COPIED: \"Copiado para a \\xe1rea de transfer\\xeancia!\",\n    SHARED: \"Compartilhado com sucesso!\"\n};\n// Configurações de localStorage\nconst STORAGE_KEYS = {\n    FAVORITES: \"floripa_guide_favorites\",\n    SEARCH_HISTORY: \"floripa_guide_search_history\",\n    USER_PREFERENCES: \"floripa_guide_preferences\",\n    VISITED_BEACHES: \"floripa_guide_visited_beaches\",\n    THEME: \"floripa_guide_theme\"\n};\n// Configurações de PWA\nconst PWA_CONFIG = {\n    name: \"Floripa Guide\",\n    shortName: \"Floripa Guide\",\n    description: \"Seu guia completo de Florian\\xf3polis\",\n    themeColor: \"#0077BE\",\n    backgroundColor: \"#ffffff\",\n    display: \"standalone\",\n    orientation: \"portrait\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/constants.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateDistance: () => (/* binding */ calculateDistance),\n/* harmony export */   celsiusToFahrenheit: () => (/* binding */ celsiusToFahrenheit),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   copyToClipboard: () => (/* binding */ copyToClipboard),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   formatCoordinates: () => (/* binding */ formatCoordinates),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   formatPrice: () => (/* binding */ formatPrice),\n/* harmony export */   formatRelativeDate: () => (/* binding */ formatRelativeDate),\n/* harmony export */   generateId: () => (/* binding */ generateId),\n/* harmony export */   generateSlug: () => (/* binding */ generateSlug),\n/* harmony export */   getUrlParams: () => (/* binding */ getUrlParams),\n/* harmony export */   isMobile: () => (/* binding */ isMobile),\n/* harmony export */   isValidEmail: () => (/* binding */ isValidEmail),\n/* harmony export */   isValidPhone: () => (/* binding */ isValidPhone),\n/* harmony export */   msToKmh: () => (/* binding */ msToKmh),\n/* harmony export */   shareContent: () => (/* binding */ shareContent),\n/* harmony export */   throttle: () => (/* binding */ throttle),\n/* harmony export */   truncateText: () => (/* binding */ truncateText),\n/* harmony export */   updateUrlParams: () => (/* binding */ updateUrlParams)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\n/**\n * Combina classes CSS de forma inteligente usando clsx e tailwind-merge\n */ function cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n/**\n * Formata números para exibição (ex: 1000 -> 1K)\n */ function formatNumber(num) {\n    if (num >= 1000000) {\n        return (num / 1000000).toFixed(1) + \"M\";\n    }\n    if (num >= 1000) {\n        return (num / 1000).toFixed(1) + \"K\";\n    }\n    return num.toString();\n}\n/**\n * Formata preços em Real brasileiro\n */ function formatPrice(price) {\n    return new Intl.NumberFormat(\"pt-BR\", {\n        style: \"currency\",\n        currency: \"BRL\"\n    }).format(price);\n}\n/**\n * Formata datas para exibição em português\n */ function formatDate(date) {\n    const dateObj = typeof date === \"string\" ? new Date(date) : date;\n    return new Intl.DateTimeFormat(\"pt-BR\", {\n        day: \"numeric\",\n        month: \"long\",\n        year: \"numeric\"\n    }).format(dateObj);\n}\n/**\n * Formata datas de forma relativa (ex: \"há 2 dias\")\n */ function formatRelativeDate(date) {\n    const dateObj = typeof date === \"string\" ? new Date(date) : date;\n    const now = new Date();\n    const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000);\n    if (diffInSeconds < 60) {\n        return \"agora mesmo\";\n    }\n    const diffInMinutes = Math.floor(diffInSeconds / 60);\n    if (diffInMinutes < 60) {\n        return `há ${diffInMinutes} minuto${diffInMinutes > 1 ? \"s\" : \"\"}`;\n    }\n    const diffInHours = Math.floor(diffInMinutes / 60);\n    if (diffInHours < 24) {\n        return `há ${diffInHours} hora${diffInHours > 1 ? \"s\" : \"\"}`;\n    }\n    const diffInDays = Math.floor(diffInHours / 24);\n    if (diffInDays < 7) {\n        return `há ${diffInDays} dia${diffInDays > 1 ? \"s\" : \"\"}`;\n    }\n    const diffInWeeks = Math.floor(diffInDays / 7);\n    if (diffInWeeks < 4) {\n        return `há ${diffInWeeks} semana${diffInWeeks > 1 ? \"s\" : \"\"}`;\n    }\n    const diffInMonths = Math.floor(diffInDays / 30);\n    return `há ${diffInMonths} mês${diffInMonths > 1 ? \"es\" : \"\"}`;\n}\n/**\n * Gera slug a partir de string\n */ function generateSlug(text) {\n    return text.toLowerCase().normalize(\"NFD\").replace(/[\\u0300-\\u036f]/g, \"\") // Remove acentos\n    .replace(/[^a-z0-9\\s-]/g, \"\") // Remove caracteres especiais\n    .trim().replace(/\\s+/g, \"-\") // Substitui espaços por hífens\n    .replace(/-+/g, \"-\"); // Remove hífens duplicados\n}\n/**\n * Trunca texto com ellipsis\n */ function truncateText(text, maxLength) {\n    if (text.length <= maxLength) return text;\n    return text.slice(0, maxLength).trim() + \"...\";\n}\n/**\n * Valida email\n */ function isValidEmail(email) {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n}\n/**\n * Valida telefone brasileiro\n */ function isValidPhone(phone) {\n    const phoneRegex = /^(\\+55\\s?)?(\\(?\\d{2}\\)?\\s?)?\\d{4,5}-?\\d{4}$/;\n    return phoneRegex.test(phone);\n}\n/**\n * Calcula distância entre duas coordenadas (em km)\n */ function calculateDistance(lat1, lon1, lat2, lon2) {\n    const R = 6371; // Raio da Terra em km\n    const dLat = (lat2 - lat1) * (Math.PI / 180);\n    const dLon = (lon2 - lon1) * (Math.PI / 180);\n    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) + Math.cos(lat1 * (Math.PI / 180)) * Math.cos(lat2 * (Math.PI / 180)) * Math.sin(dLon / 2) * Math.sin(dLon / 2);\n    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));\n    return R * c;\n}\n/**\n * Debounce function para otimizar performance\n */ function debounce(func, wait) {\n    let timeout;\n    return (...args)=>{\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\n/**\n * Throttle function para limitar execuções\n */ function throttle(func, limit) {\n    let inThrottle;\n    return (...args)=>{\n        if (!inThrottle) {\n            func(...args);\n            inThrottle = true;\n            setTimeout(()=>inThrottle = false, limit);\n        }\n    };\n}\n/**\n * Gera ID único\n */ function generateId() {\n    return Math.random().toString(36).substr(2, 9);\n}\n/**\n * Converte temperatura Celsius para Fahrenheit\n */ function celsiusToFahrenheit(celsius) {\n    return celsius * 9 / 5 + 32;\n}\n/**\n * Converte velocidade do vento m/s para km/h\n */ function msToKmh(ms) {\n    return ms * 3.6;\n}\n/**\n * Formata coordenadas para exibição\n */ function formatCoordinates(lat, lng) {\n    const latDir = lat >= 0 ? \"N\" : \"S\";\n    const lngDir = lng >= 0 ? \"E\" : \"W\";\n    return `${Math.abs(lat).toFixed(4)}°${latDir}, ${Math.abs(lng).toFixed(4)}°${lngDir}`;\n}\n/**\n * Verifica se é dispositivo móvel\n */ function isMobile() {\n    if (true) return false;\n    return window.innerWidth < 768;\n}\n/**\n * Copia texto para clipboard\n */ async function copyToClipboard(text) {\n    try {\n        await navigator.clipboard.writeText(text);\n        return true;\n    } catch (err) {\n        console.error(\"Erro ao copiar para clipboard:\", err);\n        return false;\n    }\n}\n/**\n * Compartilha conteúdo usando Web Share API\n */ async function shareContent(data) {\n    try {\n        if (navigator.share) {\n            await navigator.share(data);\n            return true;\n        }\n        // Fallback para clipboard\n        const shareText = `${data.title}\\n${data.text || \"\"}\\n${data.url || \"\"}`;\n        return await copyToClipboard(shareText);\n    } catch (err) {\n        console.error(\"Erro ao compartilhar:\", err);\n        return false;\n    }\n}\n/**\n * Obtém parâmetros da URL\n */ function getUrlParams() {\n    if (true) return new URLSearchParams();\n    return new URLSearchParams(window.location.search);\n}\n/**\n * Atualiza parâmetros da URL sem recarregar a página\n */ function updateUrlParams(params) {\n    if (true) return;\n    const url = new URL(window.location.href);\n    Object.entries(params).forEach(([key, value])=>{\n        if (value) {\n            url.searchParams.set(key, value);\n        } else {\n            url.searchParams.delete(key);\n        }\n    });\n    window.history.replaceState({}, \"\", url.toString());\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"87a8d7808819\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZmxvcmlwYS1ndWlkZS12NC8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/Mzg4ZCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjg3YThkNzgwODgxOVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Montserrat_arguments_subsets_latin_display_swap_variable_font_montserrat_weight_300_400_500_600_700_800_variableName_montserrat___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Montserrat\",\"arguments\":[{\"subsets\":[\"latin\"],\"display\":\"swap\",\"variable\":\"--font-montserrat\",\"weight\":[\"300\",\"400\",\"500\",\"600\",\"700\",\"800\"]}],\"variableName\":\"montserrat\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Montserrat\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"display\\\":\\\"swap\\\",\\\"variable\\\":\\\"--font-montserrat\\\",\\\"weight\\\":[\\\"300\\\",\\\"400\\\",\\\"500\\\",\\\"600\\\",\\\"700\\\",\\\"800\\\"]}],\\\"variableName\\\":\\\"montserrat\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Montserrat_arguments_subsets_latin_display_swap_variable_font_montserrat_weight_300_400_500_600_700_800_variableName_montserrat___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Montserrat_arguments_subsets_latin_display_swap_variable_font_montserrat_weight_300_400_500_600_700_800_variableName_montserrat___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Open_Sans_arguments_subsets_latin_display_swap_variable_font_open_sans_weight_300_400_500_600_700_variableName_openSans___WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Open_Sans\",\"arguments\":[{\"subsets\":[\"latin\"],\"display\":\"swap\",\"variable\":\"--font-open-sans\",\"weight\":[\"300\",\"400\",\"500\",\"600\",\"700\"]}],\"variableName\":\"openSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Open_Sans\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"display\\\":\\\"swap\\\",\\\"variable\\\":\\\"--font-open-sans\\\",\\\"weight\\\":[\\\"300\\\",\\\"400\\\",\\\"500\\\",\\\"600\\\",\\\"700\\\"]}],\\\"variableName\\\":\\\"openSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Open_Sans_arguments_subsets_latin_display_swap_variable_font_open_sans_weight_300_400_500_600_700_variableName_openSans___WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Open_Sans_arguments_subsets_latin_display_swap_variable_font_open_sans_weight_300_400_500_600_700_variableName_openSans___WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_layout_Header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/Header */ \"(rsc)/./src/components/layout/Header.tsx\");\n/* harmony import */ var _components_layout_Footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/layout/Footer */ \"(rsc)/./src/components/layout/Footer.tsx\");\n/* harmony import */ var _components_common_AdSense__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/common/AdSense */ \"(rsc)/./src/components/common/AdSense.tsx\");\n\n\n\n\n\n\n\n// Metadata principal do site\nconst metadata = {\n    title: {\n        default: \"Floripa Guide - Seu Guia Completo de Florian\\xf3polis 2025\",\n        template: \"%s | Floripa Guide\"\n    },\n    description: \"Descubra as 42 praias paradis\\xedacas de Florian\\xf3polis, gastronomia UNESCO, aventuras inesquec\\xedveis e dicas exclusivas para sua viagem perfeita \\xe0 Ilha da Magia.\",\n    keywords: [\n        \"florian\\xf3polis\",\n        \"praias florian\\xf3polis\",\n        \"turismo florian\\xf3polis\",\n        \"guia florian\\xf3polis\",\n        \"ilha da magia\",\n        \"praias santa catarina\",\n        \"o que fazer em florian\\xf3polis\",\n        \"roteiro florian\\xf3polis\",\n        \"gastronomia florian\\xf3polis\",\n        \"aventuras florian\\xf3polis\"\n    ],\n    authors: [\n        {\n            name: \"Floripa Guide Team\"\n        }\n    ],\n    creator: \"Floripa Guide\",\n    publisher: \"Floripa Guide\",\n    formatDetection: {\n        email: false,\n        address: false,\n        telephone: false\n    },\n    metadataBase: new URL(\"https://floripaguide.com\"),\n    alternates: {\n        canonical: \"/\",\n        languages: {\n            \"pt-BR\": \"/\",\n            \"en-US\": \"/en\",\n            \"es-ES\": \"/es\"\n        }\n    },\n    openGraph: {\n        type: \"website\",\n        locale: \"pt_BR\",\n        url: \"https://floripaguide.com\",\n        title: \"Floripa Guide - Seu Guia Completo de Florian\\xf3polis 2025\",\n        description: \"Descubra as 42 praias paradis\\xedacas de Florian\\xf3polis, gastronomia UNESCO, aventuras inesquec\\xedveis e dicas exclusivas para sua viagem perfeita \\xe0 Ilha da Magia.\",\n        siteName: \"Floripa Guide\",\n        images: [\n            {\n                url: \"/images/og-image.jpg\",\n                width: 1200,\n                height: 630,\n                alt: \"Floripa Guide - Guia Completo de Florian\\xf3polis\"\n            }\n        ]\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"Floripa Guide - Seu Guia Completo de Florian\\xf3polis 2025\",\n        description: \"Descubra as 42 praias paradis\\xedacas de Florian\\xf3polis, gastronomia UNESCO, aventuras inesquec\\xedveis e dicas exclusivas.\",\n        images: [\n            \"/images/twitter-image.jpg\"\n        ],\n        creator: \"@floripaguide\"\n    },\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            \"max-video-preview\": -1,\n            \"max-image-preview\": \"large\",\n            \"max-snippet\": -1\n        }\n    },\n    verification: {\n        google: \"your-google-verification-code\",\n        yandex: \"your-yandex-verification-code\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"pt-BR\",\n        className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Montserrat_arguments_subsets_latin_display_swap_variable_font_montserrat_weight_300_400_500_600_700_800_variableName_montserrat___WEBPACK_IMPORTED_MODULE_5___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Open_Sans_arguments_subsets_latin_display_swap_variable_font_open_sans_weight_300_400_500_600_700_variableName_openSans___WEBPACK_IMPORTED_MODULE_6___default().variable)}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/app/layout.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/app/layout.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://maps.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/app/layout.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://api.openweathermap.org\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/app/layout.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"dns-prefetch\",\n                        href: \"//www.googletagmanager.com\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/app/layout.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"dns-prefetch\",\n                        href: \"//www.google-analytics.com\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/app/layout.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"dns-prefetch\",\n                        href: \"//pagead2.googlesyndication.com\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/app/layout.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\",\n                        sizes: \"any\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/app/layout.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/icon.svg\",\n                        type: \"image/svg+xml\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/app/layout.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        href: \"/apple-touch-icon.png\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/app/layout.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/manifest.json\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/app/layout.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#0077BE\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/app/layout.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"msapplication-TileColor\",\n                        content: \"#0077BE\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/app/layout.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1, maximum-scale=5, user-scalable=yes\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/app/layout.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/app/layout.tsx\",\n                lineNumber: 110,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: \"min-h-screen bg-background font-sans antialiased\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: \"#main-content\",\n                        className: \"sr-only focus:not-sr-only focus:absolute focus:left-4 focus:top-4 z-50 bg-primary-500 text-white px-4 py-2 rounded-md\",\n                        children: \"Pular para o conte\\xfado principal\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/app/layout.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_AdSense__WEBPACK_IMPORTED_MODULE_4__.AdSenseProvider, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative flex min-h-screen flex-col\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Header__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/app/layout.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                                    id: \"main-content\",\n                                    className: \"flex-1 pt-16 lg:pt-20\",\n                                    children: children\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/app/layout.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Footer__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/app/layout.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/app/layout.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/app/layout.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/app/layout.tsx\",\n                lineNumber: 138,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/app/layout.tsx\",\n        lineNumber: 109,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/common/AdSense.tsx":
/*!*******************************************!*\
  !*** ./src/components/common/AdSense.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AdSenseProvider: () => (/* binding */ e7),
/* harmony export */   InArticleAd: () => (/* binding */ e4),
/* harmony export */   LeaderboardAd: () => (/* binding */ e0),
/* harmony export */   MobileBannerAd: () => (/* binding */ e3),
/* harmony export */   NativeAd: () => (/* binding */ e5),
/* harmony export */   RectangleAd: () => (/* binding */ e1),
/* harmony export */   SidebarAd: () => (/* binding */ e2),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__),
/* harmony export */   useAdSense: () => (/* binding */ e6)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/common/AdSense.tsx#default`));

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/common/AdSense.tsx#LeaderboardAd`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/common/AdSense.tsx#RectangleAd`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/common/AdSense.tsx#SidebarAd`);

const e3 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/common/AdSense.tsx#MobileBannerAd`);

const e4 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/common/AdSense.tsx#InArticleAd`);

const e5 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/common/AdSense.tsx#NativeAd`);

const e6 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/common/AdSense.tsx#useAdSense`);

const e7 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/common/AdSense.tsx#AdSenseProvider`);


/***/ }),

/***/ "(rsc)/./src/components/layout/Footer.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Footer.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Footer.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/components/layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/ia-sistemas/floripa-guide-v4/src/components/layout/Header.tsx#default`));


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/tailwind-merge","vendor-chunks/@radix-ui","vendor-chunks/lucide-react","vendor-chunks/class-variance-authority","vendor-chunks/@swc","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Ffloripa-guide-v4%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Falexandresimasmaciel%2FDocuments%2Fia-sistemas%2Ffloripa-guide-v4&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();