import { Metadata } from 'next';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import Section, { SectionHeader } from '@/components/layout/Section';
import Container from '@/components/layout/Container';
import { LeaderboardAd, RectangleAd, InArticleAd } from '@/components/common/AdSense';

// Metadata específica da homepage
export const metadata: Metadata = {
  title: 'Floripa Guide - Seu Guia Completo de Florianópolis 2025',
  description:
    'Descubra as 42 praias paradisíacas de Florianópolis, gastronomia UNESCO, aventuras inesquecíveis e dicas exclusivas para sua viagem perfeita à Ilha da Magia.',
  openGraph: {
    title: 'Floripa Guide - Seu Guia Completo de Florianópolis 2025',
    description:
      'Descubra as 42 praias paradisíacas de Florianópolis, gastronomia UNESCO, aventuras inesquecíveis e dicas exclusivas para sua viagem perfeita à Ilha da Magia.',
    url: 'https://floripaguide.com',
    images: [
      {
        url: '/images/homepage-og.jpg',
        width: 1200,
        height: 630,
        alt: 'Floripa Guide - Homepage',
      },
    ],
  },
};

export default function HomePage() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative h-screen flex items-center justify-center overflow-hidden -mt-16 lg:-mt-20">
        {/* Background com gradiente */}
        <div className="absolute inset-0 bg-gradient-to-br from-primary-500 via-primary-600 to-secondary-500">
          {/* Placeholder para vídeo/imagem de background */}
          <div className="absolute inset-0 bg-black/20" />
        </div>

        {/* Conteúdo do Hero */}
        <Container className="relative z-10 text-center text-white">
          <h1 className="text-hero mb-6 text-balance">
            Florianópolis 2025: Seu Guia Completo da{' '}
            <span className="text-accent-400">Ilha da Magia</span>
          </h1>
          
          <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto text-pretty opacity-90">
            42 praias paradisíacas, gastronomia UNESCO e aventuras inesquecíveis
          </p>

          {/* CTAs principais */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
            <Button variant="sand" size="xl" className="text-lg shadow-lg">
              Planeje Sua Viagem Perfeita
            </Button>
            <Button variant="outline" size="xl" className="text-lg bg-white/10 border-white/30 text-white hover:bg-white/20">
              Explorar Praias
            </Button>
          </div>

          {/* Widgets informativos */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
            {/* Widget Clima */}
            <Card className="glass border-white/20">
              <CardContent className="p-6 text-center">
                <h3 className="font-semibold mb-2">Clima Hoje</h3>
                <div className="text-2xl font-bold">28°C</div>
                <p className="text-sm opacity-80">Ensolarado</p>
              </CardContent>
            </Card>

            {/* Widget Praias Seguras */}
            <Card className="glass border-white/20">
              <CardContent className="p-6 text-center">
                <h3 className="font-semibold mb-2">Praias Seguras</h3>
                <div className="text-2xl font-bold text-green-400">35/42</div>
                <p className="text-sm opacity-80">Condições ideais</p>
              </CardContent>
            </Card>

            {/* Widget Eventos */}
            <Card className="glass border-white/20">
              <CardContent className="p-6 text-center">
                <h3 className="font-semibold mb-2">Eventos Hoje</h3>
                <div className="text-2xl font-bold">12</div>
                <p className="text-sm opacity-80">Atividades disponíveis</p>
              </CardContent>
            </Card>
          </div>
        </Container>

        {/* Scroll indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce-gentle">
          <div className="w-6 h-10 border-2 border-white/50 rounded-full flex justify-center">
            <div className="w-1 h-3 bg-white/70 rounded-full mt-2 animate-pulse" />
          </div>
        </div>
      </section>

      {/* Seção: Resolvemos Seus Problemas */}
      <Section background="gray" padding="xl">
        <SectionHeader
          title="Resolvemos Seus Problemas"
          description="Acabamos com as principais dores dos turistas em Florianópolis"
        />

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Card 1: Transporte */}
          <Card className="card-beach text-center hover:shadow-lg transition-all duration-300">
            <CardContent className="p-6">
              <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🚗</span>
              </div>
              <h3 className="text-card-title mb-3">Como fugir do trânsito infernal</h3>
              <p className="text-gray-600 text-sm">
                Rotas alternativas e horários estratégicos para evitar engarrafamentos
              </p>
            </CardContent>
          </Card>

          {/* Card 2: Multidões */}
          <Card className="card-beach text-center hover:shadow-lg transition-all duration-300">
            <CardContent className="p-6">
              <div className="w-16 h-16 bg-secondary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🏖️</span>
              </div>
              <h3 className="text-card-title mb-3">Praias secretas sem multidões</h3>
              <p className="text-gray-600 text-sm">
                Descubra praias paradisíacas longe das multidões de turistas
              </p>
            </CardContent>
          </Card>

          {/* Card 3: Transporte Alternativo */}
          <Card className="card-beach text-center hover:shadow-lg transition-all duration-300">
            <CardContent className="p-6">
              <div className="w-16 h-16 bg-accent-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🚌</span>
              </div>
              <h3 className="text-card-title mb-3">Transporte alternativo confiável</h3>
              <p className="text-gray-600 text-sm">
                Uber, 99, ônibus e transfers com preços e dicas atualizadas
              </p>
            </CardContent>
          </Card>

          {/* Card 4: Segurança */}
          <Card className="card-beach text-center hover:shadow-lg transition-all duration-300">
            <CardContent className="p-6">
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🛡️</span>
              </div>
              <h3 className="text-card-title mb-3">Proteja-se de golpes com cartão</h3>
              <p className="text-gray-600 text-sm">
                Guia completo de segurança e prevenção contra golpes turísticos
              </p>
            </CardContent>
          </Card>
        </div>
      </Section>

      {/* AdSense Leaderboard */}
      <Section background="white" padding="sm">
        <LeaderboardAd />
      </Section>

      {/* Seção: Melhores Experiências */}
      <Section background="white" padding="xl">
        <SectionHeader
          title="Melhores Experiências"
          description="Descubra o melhor de Florianópolis com nossos guias especializados"
        />

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {/* Top 10 Praias */}
          <Card className="card-beach group cursor-pointer overflow-hidden">
            <div className="aspect-card bg-gradient-to-br from-blue-400 to-blue-600 relative overflow-hidden">
              <div className="absolute inset-0 bg-black/20 group-hover:bg-black/10 transition-colors" />
              <div className="absolute bottom-4 left-4 text-white">
                <Badge variant="surf" className="mb-2">Praias</Badge>
                <h3 className="text-xl font-bold">Top 10 Praias</h3>
                <p className="text-sm opacity-90">As mais paradisíacas</p>
              </div>
            </div>
          </Card>

          {/* Restaurantes */}
          <Card className="card-beach group cursor-pointer overflow-hidden">
            <div className="aspect-card bg-gradient-to-br from-green-400 to-green-600 relative overflow-hidden">
              <div className="absolute inset-0 bg-black/20 group-hover:bg-black/10 transition-colors" />
              <div className="absolute bottom-4 left-4 text-white">
                <Badge variant="familia" className="mb-2">Gastronomia</Badge>
                <h3 className="text-xl font-bold">Restaurantes Imperdíveis</h3>
                <p className="text-sm opacity-90">Gastronomia UNESCO</p>
              </div>
            </div>
          </Card>

          {/* Aventuras */}
          <Card className="card-beach group cursor-pointer overflow-hidden">
            <div className="aspect-card bg-gradient-to-br from-orange-400 to-orange-600 relative overflow-hidden">
              <div className="absolute inset-0 bg-black/20 group-hover:bg-black/10 transition-colors" />
              <div className="absolute bottom-4 left-4 text-white">
                <Badge variant="dificil" className="mb-2">Aventura</Badge>
                <h3 className="text-xl font-bold">Aventuras Radicais</h3>
                <p className="text-sm opacity-90">Adrenalina pura</p>
              </div>
            </div>
          </Card>

          {/* Vida Noturna */}
          <Card className="card-beach group cursor-pointer overflow-hidden">
            <div className="aspect-card bg-gradient-to-br from-purple-400 to-purple-600 relative overflow-hidden">
              <div className="absolute inset-0 bg-black/20 group-hover:bg-black/10 transition-colors" />
              <div className="absolute bottom-4 left-4 text-white">
                <Badge variant="badalada" className="mb-2">Noite</Badge>
                <h3 className="text-xl font-bold">Vida Noturna</h3>
                <p className="text-sm opacity-90">Baladas e bares</p>
              </div>
            </div>
          </Card>

          {/* Cultura */}
          <Card className="card-beach group cursor-pointer overflow-hidden">
            <div className="aspect-card bg-gradient-to-br from-teal-400 to-teal-600 relative overflow-hidden">
              <div className="absolute inset-0 bg-black/20 group-hover:bg-black/10 transition-colors" />
              <div className="absolute bottom-4 left-4 text-white">
                <Badge variant="naturista" className="mb-2">Cultura</Badge>
                <h3 className="text-xl font-bold">Cultura e História</h3>
                <p className="text-sm opacity-90">Patrimônio local</p>
              </div>
            </div>
          </Card>

          {/* Roteiros */}
          <Card className="card-beach group cursor-pointer overflow-hidden">
            <div className="aspect-card bg-gradient-to-br from-pink-400 to-pink-600 relative overflow-hidden">
              <div className="absolute inset-0 bg-black/20 group-hover:bg-black/10 transition-colors" />
              <div className="absolute bottom-4 left-4 text-white">
                <Badge variant="premium" className="mb-2">Planejamento</Badge>
                <h3 className="text-xl font-bold">Roteiros Prontos</h3>
                <p className="text-sm opacity-90">Planejamento fácil</p>
              </div>
            </div>
          </Card>
        </div>
      </Section>

      {/* AdSense In-Article */}
      <InArticleAd />

      {/* CTA Final */}
      <Section background="primary" padding="xl">
        <div className="text-center">
          <h2 className="text-section-title mb-6">
            Pronto para descobrir a Ilha da Magia?
          </h2>
          <p className="text-xl mb-8 max-w-2xl mx-auto opacity-90">
            Junte-se a milhares de viajantes que já descobriram os segredos de Florianópolis
          </p>
          <Button variant="sand" size="xl" className="text-lg shadow-lg">
            Começar Agora
          </Button>
        </div>
      </Section>
    </div>
  );
}
