import { Metada<PERSON> } from 'next';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import Section, { SectionHeader } from '@/components/layout/Section';
import { 
  Waves, 
  Mountain, 
  Music, 
  Camera, 
  Plane, 
  MapPin, 
  Clock, 
  Users,
  Star
} from 'lucide-react';

export const metadata: Metadata = {
  title: 'Atividades em Florianópolis - Aventuras e Experiências',
  description: 'Descubra as melhores atividades em Florianópolis: surf, trilhas, vida noturna, parapente, mergulho e muito mais. Reserve com desconto exclusivo.',
};

const activityCategories = [
  {
    title: 'Esportes Aquáticos',
    description: 'Surf, stand-up paddle, kitesurf e mergulho',
    icon: Waves,
    color: 'bg-blue-500',
    activities: ['Aulas de Surf', 'Stand-up Paddle', 'Kitesurf', 'Mergulho'],
  },
  {
    title: '<PERSON><PERSON><PERSON> e Ecoturismo',
    description: 'Explore a natureza exuberante da ilha',
    icon: Mountain,
    color: 'bg-green-500',
    activities: ['Lagoinha do <PERSON>te', '<PERSON><PERSON> Cruz', 'Trilha do Gravatá'],
  },
  {
    title: 'Vida Noturna',
    description: 'Bares, baladas e entretenimento noturno',
    icon: Music,
    color: 'bg-purple-500',
    activities: ['Lagoa da Conceição', 'Jurerê', 'Centro Histórico'],
  },
  {
    title: 'Turismo Aéreo',
    description: 'Voos panorâmicos e parapente',
    icon: Plane,
    color: 'bg-orange-500',
    activities: ['Parapente', 'Voo de Helicóptero', 'Ultraleve'],
  },
  {
    title: 'Cultura e História',
    description: 'Museus, centros culturais e patrimônio',
    icon: Camera,
    color: 'bg-teal-500',
    activities: ['Museus', 'Centro Histórico', 'Fortalezas'],
  },
];

const featuredActivities = [
  {
    id: '1',
    title: 'Trilha da Lagoinha do Leste',
    category: 'Trilhas',
    duration: '4-6 horas',
    difficulty: 'Moderada',
    rating: 4.9,
    reviewCount: 324,
    price: 'Gratuita',
    description: 'Uma das trilhas mais bonitas do Brasil, com vista espetacular e praia selvagem no final.',
    image: 'https://images.unsplash.com/photo-1551632811-561732d1e306?w=800&h=600&fit=crop',
    highlights: ['Vista panorâmica', 'Praia selvagem', 'Natureza preservada'],
  },
  {
    id: '2',
    title: 'Aula de Surf na Joaquina',
    category: 'Esportes Aquáticos',
    duration: '2 horas',
    difficulty: 'Iniciante',
    rating: 4.7,
    reviewCount: 156,
    price: 'R$ 80',
    description: 'Aprenda a surfar na praia mais famosa de Florianópolis com instrutores experientes.',
    image: 'https://images.unsplash.com/photo-1502680390469-be75c86b636f?w=800&h=600&fit=crop',
    highlights: ['Equipamentos inclusos', 'Instrutores certificados', 'Todas as idades'],
  },
  {
    id: '3',
    title: 'Voo de Parapente',
    category: 'Turismo Aéreo',
    duration: '30 minutos',
    difficulty: 'Fácil',
    rating: 4.8,
    reviewCount: 89,
    price: 'R$ 200',
    description: 'Voe sobre as praias de Florianópolis e tenha uma vista única da Ilha da Magia.',
    image: 'https://images.unsplash.com/photo-1540979388789-6cee28a1cdc9?w=800&h=600&fit=crop',
    highlights: ['Vista aérea única', 'Segurança total', 'Fotos incluídas'],
  },
];

const getDifficultyColor = (difficulty: string) => {
  switch (difficulty) {
    case 'Fácil': return 'facil';
    case 'Moderada': return 'moderado';
    case 'Difícil': return 'dificil';
    default: return 'default';
  }
};

export default function AtividadesPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <Section background="gradient" padding="xl">
        <div className="text-center text-white">
          <h1 className="text-hero mb-6">
            Aventuras e Experiências em Florianópolis
          </h1>
          <p className="text-xl mb-8 max-w-3xl mx-auto opacity-90">
            Descubra atividades incríveis para todos os gostos: do surf radical às trilhas paradisíacas
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button variant="sand" size="lg">
              Ver Todas as Atividades
            </Button>
            <Button variant="outline" size="lg" className="bg-white/10 border-white/30 text-white hover:bg-white/20">
              Reservar Agora
            </Button>
          </div>
        </div>
      </Section>

      {/* Categorias de Atividades */}
      <Section background="white" padding="xl">
        <SectionHeader
          title="Categorias de Atividades"
          description="Escolha o tipo de aventura que mais combina com você"
        />

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {activityCategories.map((category, index) => (
            <Card key={index} className="hover:shadow-lg transition-all duration-300 cursor-pointer group">
              <CardContent className="p-6">
                <div className={`w-16 h-16 ${category.color} rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform`}>
                  <category.icon className="h-8 w-8 text-white" />
                </div>
                <h3 className="font-semibold text-lg mb-3 text-center">{category.title}</h3>
                <p className="text-gray-600 text-sm text-center mb-4">{category.description}</p>
                
                <div className="space-y-2">
                  {category.activities.map((activity, actIndex) => (
                    <div key={actIndex} className="flex items-center space-x-2 text-sm text-gray-700">
                      <div className="w-2 h-2 bg-primary-500 rounded-full"></div>
                      <span>{activity}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </Section>

      {/* Atividades em Destaque */}
      <Section background="gray" padding="xl">
        <SectionHeader
          title="Atividades Mais Populares"
          description="As experiências mais procuradas pelos visitantes"
        />

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {featuredActivities.map((activity) => (
            <Card key={activity.id} className="overflow-hidden hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
              <div className="aspect-video relative">
                <img
                  src={activity.image}
                  alt={activity.title}
                  className="w-full h-full object-cover"
                />
                <div className="absolute top-4 left-4">
                  <Badge variant="outline" className="bg-white/90 text-gray-800">
                    {activity.category}
                  </Badge>
                </div>
                <div className="absolute top-4 right-4">
                  <Badge variant={getDifficultyColor(activity.difficulty)}>
                    {activity.difficulty}
                  </Badge>
                </div>
              </div>
              
              <CardContent className="p-6">
                <div className="flex items-start justify-between mb-3">
                  <h3 className="font-semibold text-lg line-clamp-2">{activity.title}</h3>
                  <div className="flex items-center space-x-1 ml-2">
                    <Star className="h-4 w-4 text-yellow-500 fill-current" />
                    <span className="text-sm font-medium">{activity.rating}</span>
                  </div>
                </div>

                <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                  {activity.description}
                </p>

                <div className="space-y-2 mb-4">
                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center space-x-2 text-gray-600">
                      <Clock className="h-4 w-4" />
                      <span>{activity.duration}</span>
                    </div>
                    <div className="flex items-center space-x-2 text-gray-600">
                      <Users className="h-4 w-4" />
                      <span>{activity.reviewCount} avaliações</span>
                    </div>
                  </div>
                </div>

                <div className="flex flex-wrap gap-1 mb-4">
                  {activity.highlights.map((highlight, index) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {highlight}
                    </Badge>
                  ))}
                </div>

                <div className="flex items-center justify-between">
                  <div className="text-lg font-bold text-primary-600">
                    {activity.price}
                  </div>
                  <Button variant="ocean" size="sm">
                    Reservar
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="text-center mt-12">
          <Button variant="ocean" size="lg">
            Ver Todas as Atividades
          </Button>
        </div>
      </Section>

      {/* Seção de Reservas */}
      <Section background="primary" padding="xl">
        <div className="text-center text-white">
          <h2 className="text-section-title mb-6">
            Reserve com Desconto Exclusivo
          </h2>
          <p className="text-xl mb-8 max-w-3xl mx-auto opacity-90">
            Parceria com as melhores empresas de turismo da região. Desconto de até 20% para reservas antecipadas.
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
            <div className="text-center">
              <div className="text-3xl font-bold mb-2">50+</div>
              <div className="text-primary-100">Atividades Disponíveis</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold mb-2">20%</div>
              <div className="text-primary-100">Desconto Máximo</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold mb-2">24h</div>
              <div className="text-primary-100">Cancelamento Grátis</div>
            </div>
          </div>

          <Button variant="sand" size="lg">
            Explorar Ofertas
          </Button>
        </div>
      </Section>

      {/* Dicas de Segurança */}
      <Section background="white" padding="xl">
        <SectionHeader
          title="Dicas de Segurança"
          description="Informações importantes para aproveitar as atividades com segurança"
        />

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <Card>
            <CardContent className="p-6 text-center">
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">⚠️</span>
              </div>
              <h3 className="font-semibold mb-3">Condições Climáticas</h3>
              <p className="text-gray-600 text-sm">
                Sempre verifique as condições do tempo antes de atividades ao ar livre. Algumas podem ser canceladas por segurança.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6 text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🏥</span>
              </div>
              <h3 className="font-semibold mb-3">Seguro de Atividades</h3>
              <p className="text-gray-600 text-sm">
                Recomendamos contratar seguro viagem que cubra atividades de aventura. Verifique a cobertura antes de reservar.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6 text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">✅</span>
              </div>
              <h3 className="font-semibold mb-3">Empresas Certificadas</h3>
              <p className="text-gray-600 text-sm">
                Trabalhamos apenas com empresas licenciadas e com instrutores certificados. Sua segurança é nossa prioridade.
              </p>
            </CardContent>
          </Card>
        </div>
      </Section>
    </div>
  );
}
