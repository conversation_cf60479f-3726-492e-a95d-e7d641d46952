import { Metadata } from 'next';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import Section, { SectionHeader } from '@/components/layout/Section';
import Container from '@/components/layout/Container';
import SearchBar from '@/components/features/search/SearchBar';
import BeachCard from '@/components/features/beaches/BeachCard';

export const metadata: Metadata = {
  title: 'Praias de Florianópolis - Guia Completo das 42 Praias',
  description: 'Descubra as 42 praias paradisíacas de Florianópolis. Filtros avançados, condições em tempo real, mapas interativos e dicas exclusivas para cada praia.',
};

// Mock data para demonstração
const mockBeaches = [
  {
    id: '1',
    name: 'Praia da Joaquina',
    slug: 'joaquina',
    description: 'Famosa mundialmente pelo surf, com dunas impressionantes e águas cristalinas. Ideal para esportes aquáticos e aventuras.',
    region: 'Sul da Ilha',
    type: ['Surf', 'Aventura'],
    rating: 4.8,
    reviewCount: 1247,
    crowdLevel: 'medium' as const,
    waveCondition: 'moderate' as const,
    hasParking: true,
    hasRestaurants: true,
    distance: 25,
    image: 'https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=800&h=600&fit=crop',
    features: ['Surf', 'Dunas', 'Estacionamento'],
  },
  {
    id: '2',
    name: 'Jurerê Internacional',
    slug: 'jurere-internacional',
    description: 'Praia sofisticada com beach clubs exclusivos, águas calmas e infraestrutura completa. Perfeita para relaxar com estilo.',
    region: 'Norte da Ilha',
    type: ['Badalada', 'Família'],
    rating: 4.6,
    reviewCount: 892,
    crowdLevel: 'high' as const,
    waveCondition: 'calm' as const,
    hasParking: true,
    hasRestaurants: true,
    distance: 30,
    image: 'https://images.unsplash.com/photo-1507525428034-b723cf961d3e?w=800&h=600&fit=crop',
    features: ['Beach Clubs', 'Águas Calmas', 'Luxo'],
  },
  {
    id: '3',
    name: 'Lagoinha do Leste',
    slug: 'lagoinha-do-leste',
    description: 'Praia selvagem acessível apenas por trilha, considerada uma das mais belas do Brasil. Natureza preservada e vista espetacular.',
    region: 'Sul da Ilha',
    type: ['Deserta', 'Aventura'],
    rating: 4.9,
    reviewCount: 654,
    crowdLevel: 'low' as const,
    waveCondition: 'moderate' as const,
    hasParking: false,
    hasRestaurants: false,
    distance: 35,
    image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=600&fit=crop',
    features: ['Trilha', 'Natureza Preservada', 'Vista Espetacular'],
  },
];

const regions = ['Todas', 'Norte', 'Sul', 'Leste', 'Centro'];
const types = ['Todos', 'Surf', 'Família', 'Deserta', 'Badalada', 'Aventura'];

export default function PraiasPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <Section background="gradient" padding="xl">
        <div className="text-center text-white">
          <h1 className="text-hero mb-6">
            42 Praias Paradisíacas de Florianópolis
          </h1>
          <p className="text-xl mb-8 max-w-3xl mx-auto opacity-90">
            Descubra cada praia com filtros avançados, condições em tempo real e dicas exclusivas dos locais
          </p>
          
          {/* Barra de busca */}
          <div className="max-w-2xl mx-auto">
            <SearchBar 
              placeholder="Buscar praias por nome, tipo ou região..."
              className="bg-white/10 backdrop-blur-sm"
            />
          </div>
        </div>
      </Section>

      {/* Filtros */}
      <Section background="white" padding="md">
        <div className="flex flex-wrap gap-4 items-center justify-between">
          <div className="flex flex-wrap gap-3">
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium text-gray-700">Região:</span>
              <div className="flex gap-2">
                {regions.map((region) => (
                  <Badge
                    key={region}
                    variant={region === 'Todas' ? 'default' : 'outline'}
                    className="cursor-pointer hover:bg-primary-50"
                  >
                    {region}
                  </Badge>
                ))}
              </div>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <span className="text-sm text-gray-600">
              {mockBeaches.length} praias encontradas
            </span>
            <Button variant="outline" size="sm">
              Filtros Avançados
            </Button>
          </div>
        </div>
      </Section>

      {/* Lista de Praias */}
      <Section background="gray" padding="xl">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {mockBeaches.map((beach) => (
            <BeachCard
              key={beach.id}
              beach={beach}
              showDistance={true}
              showFavorite={true}
            />
          ))}
        </div>

        {/* Load More */}
        <div className="text-center mt-12">
          <Button variant="ocean" size="lg">
            Carregar Mais Praias
          </Button>
        </div>
      </Section>

      {/* Seção de Dicas */}
      <Section background="white" padding="xl">
        <SectionHeader
          title="Dicas para Aproveitar as Praias"
          description="Informações essenciais para uma experiência perfeita"
        />

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <Card className="text-center">
            <CardContent className="p-6">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🌊</span>
              </div>
              <h3 className="font-semibold mb-3">Condições do Mar</h3>
              <p className="text-gray-600 text-sm">
                Verifique sempre as condições antes de entrar na água. Respeite as bandeiras de sinalização.
              </p>
            </CardContent>
          </Card>

          <Card className="text-center">
            <CardContent className="p-6">
              <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">☀️</span>
              </div>
              <h3 className="font-semibold mb-3">Proteção Solar</h3>
              <p className="text-gray-600 text-sm">
                Use protetor solar FPS 30+, chapéu e óculos. Reaplique a cada 2 horas ou após entrar na água.
              </p>
            </CardContent>
          </Card>

          <Card className="text-center">
            <CardContent className="p-6">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🌿</span>
              </div>
              <h3 className="font-semibold mb-3">Preserve a Natureza</h3>
              <p className="text-gray-600 text-sm">
                Leve seu lixo, respeite a fauna e flora local. Ajude a manter as praias limpas para todos.
              </p>
            </CardContent>
          </Card>
        </div>
      </Section>
    </div>
  );
}
