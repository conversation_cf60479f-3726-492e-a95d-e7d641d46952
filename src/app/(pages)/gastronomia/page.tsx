import { Metadata } from 'next';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import Section, { SectionHeader } from '@/components/layout/Section';
import Container from '@/components/layout/Container';
import { Star, MapPin, Clock, DollarSign, ExternalLink } from 'lucide-react';

export const metadata: Metadata = {
  title: 'Gastronomia de Florianópolis - Patrimônio UNESCO',
  description: 'Descubra a gastronomia açoriana de Florianópolis, Patrimônio Cultural da UNESCO. Rota das ostras, sequência de camarão e os melhores restaurantes.',
};

// Mock data para demonstração
const featuredRestaurants = [
  {
    id: '1',
    name: 'Ostradamus',
    cuisine: 'Frutos do Mar',
    rating: 4.8,
    priceRange: 'Alto',
    location: 'Ribeirão da Ilha',
    specialty: 'Ostras frescas e frutos do mar',
    image: 'https://images.unsplash.com/photo-1414235077428-338989a2e8c0?w=800&h=600&fit=crop',
    openHours: '11:00 - 22:00',
    averagePrice: 'R$ 80-120',
  },
  {
    id: '2',
    name: 'Arante',
    cuisine: 'Açoriana',
    rating: 4.7,
    priceRange: 'Médio',
    location: 'Centro Histórico',
    specialty: 'Sequência de camarão tradicional',
    image: 'https://images.unsplash.com/photo-1555396273-367ea4eb4db5?w=800&h=600&fit=crop',
    openHours: '12:00 - 23:00',
    averagePrice: 'R$ 60-90',
  },
  {
    id: '3',
    name: 'Marisqueira Sintra',
    cuisine: 'Portuguesa',
    rating: 4.6,
    priceRange: 'Alto',
    location: 'Lagoa da Conceição',
    specialty: 'Caldeirada de frutos do mar',
    image: 'https://images.unsplash.com/photo-1551218808-94e220e084d2?w=800&h=600&fit=crop',
    openHours: '18:00 - 24:00',
    averagePrice: 'R$ 90-150',
  },
];

const gastronomyHighlights = [
  {
    title: 'Rota das Ostras',
    description: 'Circuito gastronômico pelos melhores produtores de ostras da região',
    icon: '🦪',
    color: 'bg-blue-500',
  },
  {
    title: 'Sequência de Camarão',
    description: 'Tradição açoriana com pratos variados de camarão',
    icon: '🦐',
    color: 'bg-orange-500',
  },
  {
    title: 'Patrimônio UNESCO',
    description: 'Gastronomia reconhecida como Patrimônio Cultural da Humanidade',
    icon: '🏆',
    color: 'bg-yellow-500',
  },
  {
    title: 'Mercado Público',
    description: 'Centro gastronômico histórico com boxes tradicionais',
    icon: '🏪',
    color: 'bg-green-500',
  },
];

export default function GastronomiaPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <Section background="gradient" padding="xl">
        <div className="text-center text-white">
          <Badge className="mb-4 bg-yellow-500 text-yellow-900">
            Patrimônio UNESCO
          </Badge>
          <h1 className="text-hero mb-6">
            Gastronomia Açoriana de Florianópolis
          </h1>
          <p className="text-xl mb-8 max-w-3xl mx-auto opacity-90">
            Descubra sabores únicos da tradição açoriana, reconhecida pela UNESCO como Patrimônio Cultural da Humanidade
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button variant="sand" size="lg">
              Rota das Ostras
            </Button>
            <Button variant="outline" size="lg" className="bg-white/10 border-white/30 text-white hover:bg-white/20">
              Mercado Público
            </Button>
          </div>
        </div>
      </Section>

      {/* Destaques da Gastronomia */}
      <Section background="white" padding="xl">
        <SectionHeader
          title="Destaques da Gastronomia Local"
          description="Experiências gastronômicas únicas que você não pode perder"
        />

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {gastronomyHighlights.map((highlight, index) => (
            <Card key={index} className="text-center hover:shadow-lg transition-all duration-300">
              <CardContent className="p-6">
                <div className={`w-16 h-16 ${highlight.color} rounded-full flex items-center justify-center mx-auto mb-4`}>
                  <span className="text-2xl">{highlight.icon}</span>
                </div>
                <h3 className="font-semibold mb-3">{highlight.title}</h3>
                <p className="text-gray-600 text-sm">{highlight.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </Section>

      {/* Restaurantes em Destaque */}
      <Section background="gray" padding="xl">
        <SectionHeader
          title="Restaurantes Imperdíveis"
          description="Os melhores estabelecimentos para experimentar a autêntica culinária local"
        />

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {featuredRestaurants.map((restaurant) => (
            <Card key={restaurant.id} className="overflow-hidden hover:shadow-lg transition-all duration-300">
              <div className="aspect-video relative">
                <img
                  src={restaurant.image}
                  alt={restaurant.name}
                  className="w-full h-full object-cover"
                />
                <div className="absolute top-4 right-4">
                  <Badge variant={restaurant.priceRange === 'Alto' ? 'premium' : 'medio'}>
                    {restaurant.priceRange}
                  </Badge>
                </div>
              </div>
              
              <CardContent className="p-6">
                <div className="flex items-start justify-between mb-3">
                  <h3 className="font-semibold text-lg">{restaurant.name}</h3>
                  <div className="flex items-center space-x-1">
                    <Star className="h-4 w-4 text-yellow-500 fill-current" />
                    <span className="text-sm font-medium">{restaurant.rating}</span>
                  </div>
                </div>

                <div className="space-y-2 mb-4">
                  <div className="flex items-center space-x-2 text-sm text-gray-600">
                    <Badge variant="outline" className="text-xs">
                      {restaurant.cuisine}
                    </Badge>
                    <span>•</span>
                    <span>{restaurant.averagePrice}</span>
                  </div>
                  
                  <div className="flex items-center space-x-2 text-sm text-gray-600">
                    <MapPin className="h-4 w-4" />
                    <span>{restaurant.location}</span>
                  </div>
                  
                  <div className="flex items-center space-x-2 text-sm text-gray-600">
                    <Clock className="h-4 w-4" />
                    <span>{restaurant.openHours}</span>
                  </div>
                </div>

                <p className="text-gray-700 text-sm mb-4">
                  <strong>Especialidade:</strong> {restaurant.specialty}
                </p>

                <div className="flex space-x-2">
                  <Button variant="ocean" size="sm" className="flex-1">
                    Ver Cardápio
                  </Button>
                  <Button variant="outline" size="sm">
                    <ExternalLink className="h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="text-center mt-12">
          <Button variant="ocean" size="lg">
            Ver Todos os Restaurantes
          </Button>
        </div>
      </Section>

      {/* Rota das Ostras */}
      <Section background="primary" padding="xl">
        <div className="text-center text-white">
          <h2 className="text-section-title mb-6">
            Rota das Ostras de Florianópolis
          </h2>
          <p className="text-xl mb-8 max-w-3xl mx-auto opacity-90">
            Circuito gastronômico pelos melhores produtores e restaurantes especializados em ostras da região
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
            <div className="text-center">
              <div className="text-3xl font-bold mb-2">12</div>
              <div className="text-primary-100">Produtores Locais</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold mb-2">25</div>
              <div className="text-primary-100">Restaurantes Parceiros</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold mb-2">8</div>
              <div className="text-primary-100">Variedades de Ostras</div>
            </div>
          </div>

          <Button variant="sand" size="lg">
            Explorar Rota das Ostras
          </Button>
        </div>
      </Section>

      {/* Dicas Gastronômicas */}
      <Section background="white" padding="xl">
        <SectionHeader
          title="Dicas para Aproveitar a Gastronomia Local"
          description="Informações essenciais para uma experiência gastronômica autêntica"
        />

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <Card>
            <CardContent className="p-6">
              <h3 className="font-semibold mb-3 flex items-center">
                <span className="text-2xl mr-3">🕐</span>
                Melhor Horário
              </h3>
              <p className="text-gray-600 text-sm">
                Para ostras, prefira o período da tarde. Para sequência de camarão, reserve com antecedência, especialmente nos fins de semana.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <h3 className="font-semibold mb-3 flex items-center">
                <span className="text-2xl mr-3">💰</span>
                Orçamento
              </h3>
              <p className="text-gray-600 text-sm">
                Sequência de camarão: R$ 60-120 por pessoa. Ostras: R$ 8-15 por unidade. Mercado Público: R$ 25-50 por refeição.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <h3 className="font-semibold mb-3 flex items-center">
                <span className="text-2xl mr-3">📍</span>
                Onde Ir
              </h3>
              <p className="text-gray-600 text-sm">
                Ribeirão da Ilha para ostras, Centro para sequência de camarão, Mercado Público para variedade e tradição.
              </p>
            </CardContent>
          </Card>
        </div>
      </Section>
    </div>
  );
}
