import type { Metada<PERSON> } from 'next';
import { Montserrat, Open_Sans } from 'next/font/google';

import './globals.css';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { AdSenseProvider } from '@/components/common/AdSense';

// Configuração das fontes
const montserrat = Montserrat({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-montserrat',
  weight: ['300', '400', '500', '600', '700', '800'],
});

const openSans = Open_Sans({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-open-sans',
  weight: ['300', '400', '500', '600', '700'],
});

// Metadata principal do site
export const metadata: Metadata = {
  title: {
    default: 'Floripa Guide - Seu Guia Completo de Florianópolis 2025',
    template: '%s | Floripa Guide',
  },
  description:
    'Descubra as 42 praias paradisíacas de Florianópolis, gastronomia UNESCO, aventuras inesquecíveis e dicas exclusivas para sua viagem perfeita à Ilha da Magia.',
  keywords: [
    'florianópolis',
    'praias florianópolis',
    'turismo florianópolis',
    'guia florianópolis',
    'ilha da magia',
    'praias santa catarina',
    'o que fazer em florianópolis',
    'roteiro florianópolis',
    'gastronomia florianópolis',
    'aventuras florianópolis',
  ],
  authors: [{ name: 'Floripa Guide Team' }],
  creator: 'Floripa Guide',
  publisher: 'Floripa Guide',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://floripaguide.com'),
  alternates: {
    canonical: '/',
    languages: {
      'pt-BR': '/',
      'en-US': '/en',
      'es-ES': '/es',
    },
  },
  openGraph: {
    type: 'website',
    locale: 'pt_BR',
    url: 'https://floripaguide.com',
    title: 'Floripa Guide - Seu Guia Completo de Florianópolis 2025',
    description:
      'Descubra as 42 praias paradisíacas de Florianópolis, gastronomia UNESCO, aventuras inesquecíveis e dicas exclusivas para sua viagem perfeita à Ilha da Magia.',
    siteName: 'Floripa Guide',
    images: [
      {
        url: '/images/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'Floripa Guide - Guia Completo de Florianópolis',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Floripa Guide - Seu Guia Completo de Florianópolis 2025',
    description:
      'Descubra as 42 praias paradisíacas de Florianópolis, gastronomia UNESCO, aventuras inesquecíveis e dicas exclusivas.',
    images: ['/images/twitter-image.jpg'],
    creator: '@floripaguide',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
    yandex: 'your-yandex-verification-code',
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang='pt-BR' className={`${montserrat.variable} ${openSans.variable}`}>
      <head>
        {/* Preconnect para performance */}
        <link rel='preconnect' href='https://fonts.googleapis.com' />
        <link rel='preconnect' href='https://fonts.gstatic.com' crossOrigin='' />
        <link rel='preconnect' href='https://maps.googleapis.com' />
        <link rel='preconnect' href='https://api.openweathermap.org' />
        
        {/* DNS Prefetch para APIs externas */}
        <link rel='dns-prefetch' href='//www.googletagmanager.com' />
        <link rel='dns-prefetch' href='//www.google-analytics.com' />
        <link rel='dns-prefetch' href='//pagead2.googlesyndication.com' />
        
        {/* Favicon e ícones */}
        <link rel='icon' href='/favicon.ico' sizes='any' />
        <link rel='icon' href='/icon.svg' type='image/svg+xml' />
        <link rel='apple-touch-icon' href='/apple-touch-icon.png' />
        <link rel='manifest' href='/manifest.json' />
        
        {/* Theme color para mobile */}
        <meta name='theme-color' content='#0077BE' />
        <meta name='msapplication-TileColor' content='#0077BE' />
        
        {/* Viewport otimizado */}
        <meta
          name='viewport'
          content='width=device-width, initial-scale=1, maximum-scale=5, user-scalable=yes'
        />
      </head>
      <body className='min-h-screen bg-background font-sans antialiased'>
        {/* Skip to main content para acessibilidade */}
        <a
          href='#main-content'
          className='sr-only focus:not-sr-only focus:absolute focus:left-4 focus:top-4 z-50 bg-primary-500 text-white px-4 py-2 rounded-md'
        >
          Pular para o conteúdo principal
        </a>
        
        {/* Estrutura principal */}
        <AdSenseProvider>
          <div className='relative flex min-h-screen flex-col'>
            {/* Header */}
            <Header />

            {/* Main content */}
            <main id='main-content' className='flex-1 pt-16 lg:pt-20'>
              {children}
            </main>

            {/* Footer */}
            <Footer />
          </div>
        </AdSenseProvider>
        
        {/* Scripts de analytics serão adicionados aqui */}
      </body>
    </html>
  );
}
