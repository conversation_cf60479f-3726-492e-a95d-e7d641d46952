'use client';

import React from 'react';
import Link from 'next/link';
import { 
  Instagram, 
  Facebook, 
  Twitter, 
  Youtube, 
  Mail, 
  Phone, 
  MapPin,
  Heart,
  ExternalLink
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import { APP_CONFIG, SOCIAL_LINKS } from '@/lib/constants';

const footerSections = [
  {
    title: 'Destinos',
    links: [
      { name: 'Praias do Norte', href: '/praias?regiao=norte' },
      { name: 'Praias do Sul', href: '/praias?regiao=sul' },
      { name: 'Praias do Leste', href: '/praias?regiao=leste' },
      { name: 'Centro Histórico', href: '/praias?regiao=centro' },
      { name: 'Lagoa da Conceição', href: '/praias/lagoa-da-conceicao' },
      { name: 'Jurerê Internacional', href: '/praias/jurere-internacional' },
    ],
  },
  {
    title: 'Experiências',
    links: [
      { name: 'Gastronomia UNESCO', href: '/gastronomia' },
      { name: 'Rota das Ostras', href: '/gastronomia/rota-das-ostras' },
      { name: 'Trilhas e Ecoturismo', href: '/atividades/trilhas' },
      { name: 'Esportes Aquáticos', href: '/atividades/esportes-aquaticos' },
      { name: 'Vida Noturna', href: '/atividades/vida-noturna' },
      { name: 'Cultura Açoriana', href: '/atividades/cultura' },
    ],
  },
  {
    title: 'Planejamento',
    links: [
      { name: 'Roteiros Prontos', href: '/planejador/roteiros' },
      { name: 'Calculadora de Orçamento', href: '/planejador/orcamento' },
      { name: 'Quando Visitar', href: '/planejador/quando-visitar' },
      { name: 'Como Chegar', href: '/solucoes/transporte' },
      { name: 'Onde Ficar', href: '/hospedagem' },
      { name: 'Seguro Viagem', href: '/seguro-viagem' },
    ],
  },
  {
    title: 'Recursos',
    links: [
      { name: 'Guia de Segurança', href: '/solucoes/seguranca' },
      { name: 'Transporte Local', href: '/solucoes/transporte' },
      { name: 'Clima e Condições', href: '/clima' },
      { name: 'Eventos 2025', href: '/eventos' },
      { name: 'Blog', href: '/blog' },
      { name: 'Contato', href: '/contato' },
    ],
  },
];

const socialLinks = [
  { name: 'Instagram', href: SOCIAL_LINKS.INSTAGRAM, icon: Instagram },
  { name: 'Facebook', href: SOCIAL_LINKS.FACEBOOK, icon: Facebook },
  { name: 'Twitter', href: SOCIAL_LINKS.TWITTER, icon: Twitter },
  { name: 'YouTube', href: SOCIAL_LINKS.YOUTUBE, icon: Youtube },
];

export default function Footer() {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-gray-900 text-white">
      {/* Newsletter Section */}
      <div className="bg-primary-500">
        <div className="container-custom py-12">
          <div className="max-w-4xl mx-auto text-center">
            <h3 className="text-2xl font-bold mb-4">
              Receba as melhores dicas de Florianópolis
            </h3>
            <p className="text-primary-100 mb-8 max-w-2xl mx-auto">
              Cadastre-se na nossa newsletter e receba roteiros exclusivos, 
              dicas de locais, ofertas especiais e muito mais!
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
              <Input
                type="email"
                placeholder="Seu melhor e-mail"
                className="bg-white/10 border-white/20 text-white placeholder:text-white/70 focus:border-white"
              />
              <Button variant="sand" size="lg" className="whitespace-nowrap">
                <Mail className="h-4 w-4 mr-2" />
                Inscrever-se
              </Button>
            </div>
            
            <p className="text-xs text-primary-100 mt-4">
              Sem spam. Cancele quando quiser. 📧
            </p>
          </div>
        </div>
      </div>

      {/* Main Footer Content */}
      <div className="container-custom py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8">
          {/* Brand Section */}
          <div className="lg:col-span-2">
            <Link href="/" className="flex items-center space-x-3 mb-6">
              <div className="w-12 h-12 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-xl">FG</span>
              </div>
              <div>
                <h3 className="font-heading font-bold text-xl">Floripa Guide</h3>
                <p className="text-gray-400 text-sm">Ilha da Magia</p>
              </div>
            </Link>
            
            <p className="text-gray-300 mb-6 leading-relaxed">
              Seu guia completo para descobrir as 42 praias paradisíacas, 
              gastronomia UNESCO e aventuras inesquecíveis de Florianópolis.
            </p>

            {/* Contact Info */}
            <div className="space-y-3 mb-6">
              <div className="flex items-center space-x-3 text-sm text-gray-300">
                <MapPin className="h-4 w-4 text-primary-400" />
                <span>Florianópolis, Santa Catarina</span>
              </div>
              <div className="flex items-center space-x-3 text-sm text-gray-300">
                <Phone className="h-4 w-4 text-primary-400" />
                <span>{APP_CONFIG.phone}</span>
              </div>
              <div className="flex items-center space-x-3 text-sm text-gray-300">
                <Mail className="h-4 w-4 text-primary-400" />
                <span>{APP_CONFIG.email}</span>
              </div>
            </div>

            {/* Social Links */}
            <div className="flex space-x-4">
              {socialLinks.map((social) => (
                <Link
                  key={social.name}
                  href={social.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-primary-500 transition-colors group"
                >
                  <social.icon className="h-5 w-5 text-gray-400 group-hover:text-white" />
                </Link>
              ))}
            </div>
          </div>

          {/* Footer Links */}
          {footerSections.map((section) => (
            <div key={section.title}>
              <h4 className="font-semibold text-white mb-4">{section.title}</h4>
              <ul className="space-y-3">
                {section.links.map((link) => (
                  <li key={link.name}>
                    <Link
                      href={link.href}
                      className="text-gray-300 hover:text-primary-400 transition-colors text-sm"
                    >
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      </div>

      <Separator className="bg-gray-800" />

      {/* Bottom Footer */}
      <div className="container-custom py-8">
        <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
          <div className="flex flex-col md:flex-row items-center space-y-2 md:space-y-0 md:space-x-6 text-sm text-gray-400">
            <p>
              © {currentYear} {APP_CONFIG.name}. Todos os direitos reservados.
            </p>
            <div className="flex items-center space-x-4">
              <Link href="/privacidade" className="hover:text-primary-400 transition-colors">
                Privacidade
              </Link>
              <Link href="/termos" className="hover:text-primary-400 transition-colors">
                Termos
              </Link>
              <Link href="/cookies" className="hover:text-primary-400 transition-colors">
                Cookies
              </Link>
            </div>
          </div>

          <div className="flex items-center space-x-2 text-sm text-gray-400">
            <span>Feito com</span>
            <Heart className="h-4 w-4 text-red-500" />
            <span>para os amantes da Ilha da Magia</span>
            <span className="text-lg">🏝️</span>
          </div>
        </div>

        {/* Affiliate Disclaimer */}
        <div className="mt-6 pt-6 border-t border-gray-800">
          <p className="text-xs text-gray-500 text-center max-w-4xl mx-auto">
            <strong>Aviso:</strong> Este site contém links de afiliados. Quando você clica em um link e faz uma compra, 
            podemos receber uma pequena comissão sem custo adicional para você. Isso nos ajuda a manter o site 
            funcionando e continuar fornecendo conteúdo gratuito e de qualidade.
          </p>
        </div>
      </div>
    </footer>
  );
}
