import React from 'react';
import { cn } from '@/lib/utils';
import Container from './Container';

interface SectionProps {
  children: React.ReactNode;
  className?: string;
  containerClassName?: string;
  background?: 'white' | 'gray' | 'primary' | 'secondary' | 'gradient' | 'transparent';
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  containerSize?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  id?: string;
}

const backgroundClasses = {
  white: 'bg-white',
  gray: 'bg-gray-50',
  primary: 'bg-primary-500 text-white',
  secondary: 'bg-secondary-500 text-white',
  gradient: 'bg-gradient-to-br from-primary-500 to-secondary-500 text-white',
  transparent: 'bg-transparent',
};

const paddingClasses = {
  none: '',
  sm: 'py-8',
  md: 'py-12',
  lg: 'py-16 lg:py-20',
  xl: 'py-20 lg:py-24',
};

export default function Section({
  children,
  className,
  containerClassName,
  background = 'transparent',
  padding = 'lg',
  containerSize = 'lg',
  id,
}: SectionProps) {
  return (
    <section
      id={id}
      className={cn(
        backgroundClasses[background],
        paddingClasses[padding],
        className
      )}
    >
      <Container size={containerSize} className={containerClassName}>
        {children}
      </Container>
    </section>
  );
}

// Componente para título de seção
interface SectionHeaderProps {
  title: string;
  subtitle?: string;
  description?: string;
  className?: string;
  align?: 'left' | 'center' | 'right';
}

export function SectionHeader({
  title,
  subtitle,
  description,
  className,
  align = 'center',
}: SectionHeaderProps) {
  const alignClasses = {
    left: 'text-left',
    center: 'text-center',
    right: 'text-right',
  };

  return (
    <div className={cn('mb-12 lg:mb-16', alignClasses[align], className)}>
      {subtitle && (
        <p className="text-sm font-semibold text-primary-500 uppercase tracking-wide mb-3">
          {subtitle}
        </p>
      )}
      <h2 className="text-section-title mb-4 text-balance">
        {title}
      </h2>
      {description && (
        <p className="text-lg text-gray-600 max-w-3xl mx-auto text-pretty">
          {description}
        </p>
      )}
    </div>
  );
}
