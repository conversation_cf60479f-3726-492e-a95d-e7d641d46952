'use client';

import React, { useEffect } from 'react';
import { cn } from '@/lib/utils';

interface AdSenseProps {
  slot: string;
  format?: 'auto' | 'rectangle' | 'vertical' | 'horizontal';
  responsive?: boolean;
  className?: string;
  style?: React.CSSProperties;
}

// Configurações de tamanhos dos anúncios
const adFormats = {
  auto: { width: 'auto', height: 'auto' },
  rectangle: { width: 300, height: 250 },
  vertical: { width: 160, height: 600 },
  horizontal: { width: 728, height: 90 },
};

declare global {
  interface Window {
    adsbygoogle: any[];
  }
}

export default function AdSense({
  slot,
  format = 'auto',
  responsive = true,
  className,
  style,
}: AdSenseProps) {
  const adClient = process.env.NEXT_PUBLIC_ADSENSE_CLIENT_ID;

  useEffect(() => {
    // Verificar se o AdSense já foi carregado
    if (typeof window !== 'undefined') {
      try {
        // Inicializar o array se não existir
        if (!window.adsbygoogle) {
          window.adsbygoogle = [];
        }
        
        // Push do anúncio para a fila
        window.adsbygoogle.push({});
      } catch (error) {
        console.error('Erro ao carregar AdSense:', error);
      }
    }
  }, []);

  // Não renderizar se não tiver client ID configurado
  if (!adClient) {
    return (
      <div className={cn('bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg p-4 text-center', className)}>
        <p className="text-gray-500 text-sm">
          AdSense não configurado
        </p>
        <p className="text-xs text-gray-400 mt-1">
          Configure NEXT_PUBLIC_ADSENSE_CLIENT_ID
        </p>
      </div>
    );
  }

  const adStyle = {
    display: 'block',
    ...adFormats[format],
    ...style,
  };

  return (
    <div className={cn('ad-container', className)}>
      <ins
        className="adsbygoogle"
        style={adStyle}
        data-ad-client={adClient}
        data-ad-slot={slot}
        data-ad-format={responsive ? 'auto' : format}
        data-full-width-responsive={responsive ? 'true' : 'false'}
      />
    </div>
  );
}

// Componentes pré-configurados para diferentes posições

export function LeaderboardAd({ className }: { className?: string }) {
  return (
    <AdSense
      slot="1234567890" // Substitua pelo slot real
      format="horizontal"
      className={cn('my-8', className)}
    />
  );
}

export function RectangleAd({ className }: { className?: string }) {
  return (
    <AdSense
      slot="1234567891" // Substitua pelo slot real
      format="rectangle"
      className={cn('my-6', className)}
    />
  );
}

export function SidebarAd({ className }: { className?: string }) {
  return (
    <AdSense
      slot="1234567892" // Substitua pelo slot real
      format="vertical"
      className={cn('sticky top-4', className)}
    />
  );
}

export function MobileBannerAd({ className }: { className?: string }) {
  return (
    <div className={cn('block md:hidden', className)}>
      <AdSense
        slot="1234567893" // Substitua pelo slot real
        format="auto"
        responsive={true}
      />
    </div>
  );
}

export function InArticleAd({ className }: { className?: string }) {
  return (
    <div className={cn('my-8 flex justify-center', className)}>
      <AdSense
        slot="1234567894" // Substitua pelo slot real
        format="rectangle"
        responsive={true}
      />
    </div>
  );
}

// Componente para anúncios nativos
export function NativeAd({ className }: { className?: string }) {
  return (
    <div className={cn('my-6', className)}>
      <div className="text-xs text-gray-500 mb-2 text-center">Publicidade</div>
      <AdSense
        slot="1234567895" // Substitua pelo slot real
        format="auto"
        responsive={true}
      />
    </div>
  );
}

// Hook para carregar o script do AdSense
export function useAdSense() {
  useEffect(() => {
    const adClient = process.env.NEXT_PUBLIC_ADSENSE_CLIENT_ID;
    
    if (!adClient || typeof window === 'undefined') return;

    // Verificar se o script já foi carregado
    const existingScript = document.querySelector(
      `script[src*="pagead2.googlesyndication.com/pagead/js/adsbygoogle.js"]`
    );

    if (!existingScript) {
      const script = document.createElement('script');
      script.async = true;
      script.src = `https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=${adClient}`;
      script.crossOrigin = 'anonymous';
      
      script.onerror = () => {
        console.error('Erro ao carregar script do AdSense');
      };

      document.head.appendChild(script);
    }
  }, []);
}

// Componente para carregar o AdSense globalmente
export function AdSenseProvider({ children }: { children: React.ReactNode }) {
  useAdSense();
  return <>{children}</>;
}
