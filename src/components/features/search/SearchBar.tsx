'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Search, MapPin, Utensils, Activity, X } from 'lucide-react';

import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { cn } from '@/lib/utils';

interface SearchResult {
  id: string;
  title: string;
  type: 'beach' | 'restaurant' | 'activity';
  description: string;
  location: string;
  image?: string;
  href: string;
}

interface SearchBarProps {
  placeholder?: string;
  className?: string;
  onSearch?: (query: string) => void;
  showResults?: boolean;
  autoFocus?: boolean;
}

// Mock data para demonstração
const mockResults: SearchResult[] = [
  {
    id: '1',
    title: 'Praia da Joaquina',
    type: 'beach',
    description: 'Famosa praia de surf com dunas e águas cristalinas',
    location: 'Sul da Ilha',
    href: '/praias/joaquina',
  },
  {
    id: '2',
    title: 'Ostradamus',
    type: 'restaurant',
    description: 'Restaurante especializado em ostras e frutos do mar',
    location: 'Ribeirão da Ilha',
    href: '/gastronomia/ostradamus',
  },
  {
    id: '3',
    title: 'Trilha da Lagoinha do Leste',
    type: 'activity',
    description: 'Trilha moderada com vista espetacular',
    location: 'Sul da Ilha',
    href: '/atividades/trilha-lagoinha-leste',
  },
];

const typeIcons = {
  beach: MapPin,
  restaurant: Utensils,
  activity: Activity,
};

const typeLabels = {
  beach: 'Praia',
  restaurant: 'Restaurante',
  activity: 'Atividade',
};

const typeBadgeVariants = {
  beach: 'surf' as const,
  restaurant: 'familia' as const,
  activity: 'dificil' as const,
};

export default function SearchBar({
  placeholder = 'Buscar praias, restaurantes, atividades...',
  className,
  onSearch,
  showResults = true,
  autoFocus = false,
}: SearchBarProps) {
  const [query, setQuery] = useState('');
  const [isOpen, setIsOpen] = useState(false);
  const [results, setResults] = useState<SearchResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Simular busca
  useEffect(() => {
    if (query.length > 2) {
      setIsLoading(true);
      const timer = setTimeout(() => {
        const filtered = mockResults.filter(
          (result) =>
            result.title.toLowerCase().includes(query.toLowerCase()) ||
            result.description.toLowerCase().includes(query.toLowerCase()) ||
            result.location.toLowerCase().includes(query.toLowerCase())
        );
        setResults(filtered);
        setIsLoading(false);
        setIsOpen(true);
      }, 300);

      return () => clearTimeout(timer);
    } else {
      setResults([]);
      setIsOpen(false);
    }
  }, [query]);

  // Fechar ao clicar fora
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        containerRef.current &&
        !containerRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleSearch = (searchQuery: string) => {
    onSearch?.(searchQuery);
    setIsOpen(false);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch(query);
    }
    if (e.key === 'Escape') {
      setIsOpen(false);
      inputRef.current?.blur();
    }
  };

  const clearSearch = () => {
    setQuery('');
    setResults([]);
    setIsOpen(false);
    inputRef.current?.focus();
  };

  return (
    <div ref={containerRef} className={cn('relative w-full', className)}>
      {/* Input de busca */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
        <Input
          ref={inputRef}
          type="text"
          placeholder={placeholder}
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          onKeyDown={handleKeyDown}
          onFocus={() => query.length > 2 && setIsOpen(true)}
          className="pl-10 pr-10 py-3 text-base"
          autoFocus={autoFocus}
        />
        {query && (
          <Button
            variant="ghost"
            size="icon"
            onClick={clearSearch}
            className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6"
          >
            <X className="h-4 w-4" />
          </Button>
        )}
      </div>

      {/* Resultados da busca */}
      {showResults && isOpen && (
        <Card className="absolute top-full left-0 right-0 mt-2 z-50 shadow-lg border-gray-200">
          <CardContent className="p-0">
            {isLoading ? (
              <div className="p-4 text-center text-gray-500">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-500 mx-auto mb-2"></div>
                Buscando...
              </div>
            ) : results.length > 0 ? (
              <div className="max-h-96 overflow-y-auto">
                {results.map((result) => {
                  const Icon = typeIcons[result.type];
                  return (
                    <div
                      key={result.id}
                      className="p-4 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0 transition-colors"
                      onClick={() => handleSearch(result.title)}
                    >
                      <div className="flex items-start space-x-3">
                        <div className="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center flex-shrink-0">
                          <Icon className="h-5 w-5 text-primary-600" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center space-x-2 mb-1">
                            <h4 className="font-medium text-gray-900 truncate">
                              {result.title}
                            </h4>
                            <Badge variant={typeBadgeVariants[result.type]} className="text-xs">
                              {typeLabels[result.type]}
                            </Badge>
                          </div>
                          <p className="text-sm text-gray-600 line-clamp-2 mb-1">
                            {result.description}
                          </p>
                          <p className="text-xs text-gray-500 flex items-center">
                            <MapPin className="h-3 w-3 mr-1" />
                            {result.location}
                          </p>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            ) : query.length > 2 ? (
              <div className="p-4 text-center text-gray-500">
                <Search className="h-8 w-8 mx-auto mb-2 text-gray-300" />
                <p>Nenhum resultado encontrado para "{query}"</p>
                <p className="text-sm mt-1">
                  Tente buscar por praias, restaurantes ou atividades
                </p>
              </div>
            ) : null}

            {/* Sugestões populares */}
            {!query && (
              <div className="p-4 border-t border-gray-100">
                <p className="text-sm font-medium text-gray-700 mb-3">
                  Buscas populares:
                </p>
                <div className="flex flex-wrap gap-2">
                  {['Joaquina', 'Jurerê', 'Lagoa da Conceição', 'Ostras', 'Trilhas'].map(
                    (suggestion) => (
                      <Badge
                        key={suggestion}
                        variant="outline"
                        className="cursor-pointer hover:bg-primary-50 hover:border-primary-200"
                        onClick={() => {
                          setQuery(suggestion);
                          handleSearch(suggestion);
                        }}
                      >
                        {suggestion}
                      </Badge>
                    )
                  )}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
