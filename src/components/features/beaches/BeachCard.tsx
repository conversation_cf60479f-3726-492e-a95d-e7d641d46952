'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { 
  Heart, 
  MapPin, 
  Star, 
  Users, 
  Waves, 
  Car, 
  Utensils,
  Camera,
  ExternalLink
} from 'lucide-react';

import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

interface Beach {
  id: string;
  name: string;
  slug: string;
  description: string;
  region: string;
  type: string[];
  rating: number;
  reviewCount: number;
  crowdLevel: 'low' | 'medium' | 'high';
  waveCondition: 'calm' | 'moderate' | 'rough';
  hasParking: boolean;
  hasRestaurants: boolean;
  distance?: number;
  image: string;
  images?: string[];
  features: string[];
}

interface BeachCardProps {
  beach: Beach;
  className?: string;
  showFavorite?: boolean;
  showDistance?: boolean;
  size?: 'small' | 'medium' | 'large';
  layout?: 'vertical' | 'horizontal';
}

const crowdLevelLabels = {
  low: 'Tranquila',
  medium: 'Moderada',
  high: 'Movimentada',
};

const crowdLevelColors = {
  low: 'bg-green-100 text-green-800',
  medium: 'bg-yellow-100 text-yellow-800',
  high: 'bg-red-100 text-red-800',
};

const waveConditionLabels = {
  calm: 'Mar calmo',
  moderate: 'Ondas moderadas',
  rough: 'Mar agitado',
};

const waveConditionColors = {
  calm: 'text-green-600',
  moderate: 'text-yellow-600',
  rough: 'text-red-600',
};

const typeVariants: Record<string, any> = {
  'Surf': 'surf',
  'Família': 'familia',
  'Deserta': 'deserta',
  'Badalada': 'badalada',
  'Naturista': 'naturista',
};

export default function BeachCard({
  beach,
  className,
  showFavorite = true,
  showDistance = false,
  size = 'medium',
  layout = 'vertical',
}: BeachCardProps) {
  const [isFavorite, setIsFavorite] = useState(false);
  const [imageError, setImageError] = useState(false);

  const toggleFavorite = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsFavorite(!isFavorite);
    // Aqui você salvaria no localStorage ou enviaria para API
  };

  const cardSizes = {
    small: 'w-full max-w-sm',
    medium: 'w-full max-w-md',
    large: 'w-full max-w-lg',
  };

  const imageSizes = {
    small: layout === 'horizontal' ? 'w-24 h-24' : 'h-48',
    medium: layout === 'horizontal' ? 'w-32 h-32' : 'h-56',
    large: layout === 'horizontal' ? 'w-40 h-40' : 'h-64',
  };

  if (layout === 'horizontal') {
    return (
      <Card className={cn(
        'group hover:shadow-lg transition-all duration-300 overflow-hidden',
        cardSizes[size],
        className
      )}>
        <Link href={`/praias/${beach.slug}`}>
          <CardContent className="p-0">
            <div className="flex">
              {/* Imagem */}
              <div className={cn('relative flex-shrink-0', imageSizes[size])}>
                {!imageError ? (
                  <Image
                    src={beach.image}
                    alt={beach.name}
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-300"
                    onError={() => setImageError(true)}
                  />
                ) : (
                  <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                    <Camera className="h-8 w-8 text-gray-400" />
                  </div>
                )}
                
                {/* Favorite button */}
                {showFavorite && (
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={toggleFavorite}
                    className="absolute top-2 right-2 h-8 w-8 bg-white/80 hover:bg-white"
                  >
                    <Heart
                      className={cn(
                        'h-4 w-4',
                        isFavorite ? 'fill-red-500 text-red-500' : 'text-gray-600'
                      )}
                    />
                  </Button>
                )}
              </div>

              {/* Conteúdo */}
              <div className="flex-1 p-4">
                <div className="flex items-start justify-between mb-2">
                  <h3 className="font-semibold text-lg group-hover:text-primary-600 transition-colors line-clamp-1">
                    {beach.name}
                  </h3>
                  {showDistance && beach.distance && (
                    <span className="text-sm text-gray-500 ml-2">
                      {beach.distance}km
                    </span>
                  )}
                </div>

                <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                  {beach.description}
                </p>

                {/* Badges */}
                <div className="flex flex-wrap gap-1 mb-3">
                  {beach.type.slice(0, 2).map((type) => (
                    <Badge
                      key={type}
                      variant={typeVariants[type] || 'default'}
                      className="text-xs"
                    >
                      {type}
                    </Badge>
                  ))}
                </div>

                {/* Info row */}
                <div className="flex items-center justify-between text-sm">
                  <div className="flex items-center space-x-3">
                    <div className="flex items-center space-x-1">
                      <Star className="h-4 w-4 text-yellow-500 fill-current" />
                      <span className="font-medium">{beach.rating}</span>
                      <span className="text-gray-500">({beach.reviewCount})</span>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <MapPin className="h-4 w-4 text-gray-400" />
                    <span className="text-gray-600">{beach.region}</span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Link>
      </Card>
    );
  }

  return (
    <Card className={cn(
      'group hover:shadow-lg transition-all duration-300 overflow-hidden hover:-translate-y-1',
      cardSizes[size],
      className
    )}>
      <Link href={`/praias/${beach.slug}`}>
        <CardContent className="p-0">
          {/* Imagem */}
          <div className={cn('relative', imageSizes[size])}>
            {!imageError ? (
              <Image
                src={beach.image}
                alt={beach.name}
                fill
                className="object-cover group-hover:scale-105 transition-transform duration-300"
                onError={() => setImageError(true)}
              />
            ) : (
              <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                <Camera className="h-12 w-12 text-gray-400" />
              </div>
            )}
            
            {/* Overlay gradient */}
            <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent" />
            
            {/* Favorite button */}
            {showFavorite && (
              <Button
                variant="ghost"
                size="icon"
                onClick={toggleFavorite}
                className="absolute top-3 right-3 h-9 w-9 bg-white/80 hover:bg-white"
              >
                <Heart
                  className={cn(
                    'h-5 w-5',
                    isFavorite ? 'fill-red-500 text-red-500' : 'text-gray-600'
                  )}
                />
              </Button>
            )}

            {/* Distance badge */}
            {showDistance && beach.distance && (
              <Badge className="absolute top-3 left-3 bg-black/60 text-white border-0">
                {beach.distance}km
              </Badge>
            )}

            {/* Crowd level indicator */}
            <div className="absolute bottom-3 left-3">
              <Badge className={cn('text-xs', crowdLevelColors[beach.crowdLevel])}>
                <Users className="h-3 w-3 mr-1" />
                {crowdLevelLabels[beach.crowdLevel]}
              </Badge>
            </div>

            {/* Wave condition */}
            <div className="absolute bottom-3 right-3">
              <Waves className={cn('h-5 w-5', waveConditionColors[beach.waveCondition])} />
            </div>
          </div>

          {/* Conteúdo */}
          <div className="p-4">
            <div className="flex items-start justify-between mb-2">
              <h3 className="font-semibold text-lg group-hover:text-primary-600 transition-colors line-clamp-1">
                {beach.name}
              </h3>
              <div className="flex items-center space-x-1 ml-2">
                <Star className="h-4 w-4 text-yellow-500 fill-current" />
                <span className="font-medium text-sm">{beach.rating}</span>
              </div>
            </div>

            <p className="text-gray-600 text-sm mb-3 line-clamp-2">
              {beach.description}
            </p>

            {/* Type badges */}
            <div className="flex flex-wrap gap-1 mb-3">
              {beach.type.map((type) => (
                <Badge
                  key={type}
                  variant={typeVariants[type] || 'default'}
                  className="text-xs"
                >
                  {type}
                </Badge>
              ))}
            </div>

            {/* Features */}
            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center space-x-3">
                {beach.hasParking && (
                  <div className="flex items-center space-x-1 text-gray-600">
                    <Car className="h-4 w-4" />
                    <span>Estacionamento</span>
                  </div>
                )}
                {beach.hasRestaurants && (
                  <div className="flex items-center space-x-1 text-gray-600">
                    <Utensils className="h-4 w-4" />
                    <span>Restaurantes</span>
                  </div>
                )}
              </div>
              
              <div className="flex items-center space-x-1 text-gray-500">
                <MapPin className="h-4 w-4" />
                <span>{beach.region}</span>
              </div>
            </div>

            {/* Reviews */}
            <div className="flex items-center justify-between mt-3 pt-3 border-t border-gray-100">
              <span className="text-sm text-gray-500">
                {beach.reviewCount} avaliações
              </span>
              <span className="text-sm text-gray-600">
                {waveConditionLabels[beach.waveCondition]}
              </span>
            </div>
          </div>
        </CardContent>
      </Link>
    </Card>
  );
}
