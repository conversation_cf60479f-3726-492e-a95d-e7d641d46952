'use client';

import React, { useState, useEffect } from 'react';
import { 
  Sun, 
  Cloud, 
  CloudRain, 
  Wind, 
  Droplets, 
  Eye, 
  Thermometer,
  RefreshCw
} from 'lucide-react';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

interface WeatherData {
  temperature: number;
  condition: string;
  description: string;
  humidity: number;
  windSpeed: number;
  visibility: number;
  uvIndex: number;
  feelsLike: number;
  icon: string;
}

interface WeatherWidgetProps {
  location?: string;
  className?: string;
  compact?: boolean;
  showDetails?: boolean;
}

// Mock data para demonstração
const mockWeatherData: WeatherData = {
  temperature: 28,
  condition: 'sunny',
  description: 'Ensolarado',
  humidity: 65,
  windSpeed: 12,
  visibility: 10,
  uvIndex: 8,
  feelsLike: 31,
  icon: '☀️',
};

const weatherIcons = {
  sunny: Sun,
  cloudy: Cloud,
  rainy: CloudRain,
  'partly-cloudy': Cloud,
};

const weatherColors = {
  sunny: 'text-yellow-500',
  cloudy: 'text-gray-500',
  rainy: 'text-blue-500',
  'partly-cloudy': 'text-gray-400',
};

const getUVIndexColor = (uvIndex: number) => {
  if (uvIndex <= 2) return 'bg-green-500';
  if (uvIndex <= 5) return 'bg-yellow-500';
  if (uvIndex <= 7) return 'bg-orange-500';
  if (uvIndex <= 10) return 'bg-red-500';
  return 'bg-purple-500';
};

const getUVIndexLabel = (uvIndex: number) => {
  if (uvIndex <= 2) return 'Baixo';
  if (uvIndex <= 5) return 'Moderado';
  if (uvIndex <= 7) return 'Alto';
  if (uvIndex <= 10) return 'Muito Alto';
  return 'Extremo';
};

export default function WeatherWidget({
  location = 'Florianópolis',
  className,
  compact = false,
  showDetails = true,
}: WeatherWidgetProps) {
  const [weather, setWeather] = useState<WeatherData>(mockWeatherData);
  const [isLoading, setIsLoading] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());

  const WeatherIcon = weatherIcons[weather.condition as keyof typeof weatherIcons] || Sun;
  const iconColor = weatherColors[weather.condition as keyof typeof weatherColors] || 'text-yellow-500';

  const refreshWeather = async () => {
    setIsLoading(true);
    // Simular chamada de API
    setTimeout(() => {
      setWeather({
        ...mockWeatherData,
        temperature: Math.round(Math.random() * 10 + 25), // 25-35°C
        windSpeed: Math.round(Math.random() * 15 + 5), // 5-20 km/h
        humidity: Math.round(Math.random() * 30 + 50), // 50-80%
      });
      setLastUpdated(new Date());
      setIsLoading(false);
    }, 1000);
  };

  useEffect(() => {
    // Atualizar automaticamente a cada 30 minutos
    const interval = setInterval(refreshWeather, 30 * 60 * 1000);
    return () => clearInterval(interval);
  }, []);

  if (compact) {
    return (
      <Card className={cn('w-full', className)}>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <WeatherIcon className={cn('h-8 w-8', iconColor)} />
              <div>
                <div className="text-2xl font-bold">{weather.temperature}°C</div>
                <div className="text-sm text-gray-600">{weather.description}</div>
              </div>
            </div>
            <div className="text-right">
              <div className="text-sm font-medium">{location}</div>
              <div className="text-xs text-gray-500">
                {lastUpdated.toLocaleTimeString('pt-BR', { 
                  hour: '2-digit', 
                  minute: '2-digit' 
                })}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn('w-full', className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold">
            Clima em {location}
          </CardTitle>
          <Button
            variant="ghost"
            size="icon"
            onClick={refreshWeather}
            disabled={isLoading}
            className="h-8 w-8"
          >
            <RefreshCw className={cn('h-4 w-4', isLoading && 'animate-spin')} />
          </Button>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Temperatura principal */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <WeatherIcon className={cn('h-12 w-12', iconColor)} />
            <div>
              <div className="text-3xl font-bold">{weather.temperature}°C</div>
              <div className="text-gray-600">{weather.description}</div>
              <div className="text-sm text-gray-500">
                Sensação: {weather.feelsLike}°C
              </div>
            </div>
          </div>
        </div>

        {showDetails && (
          <>
            {/* Detalhes do clima */}
            <div className="grid grid-cols-2 gap-4 pt-4 border-t border-gray-100">
              <div className="flex items-center space-x-2">
                <Wind className="h-4 w-4 text-gray-400" />
                <div>
                  <div className="text-sm font-medium">{weather.windSpeed} km/h</div>
                  <div className="text-xs text-gray-500">Vento</div>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Droplets className="h-4 w-4 text-gray-400" />
                <div>
                  <div className="text-sm font-medium">{weather.humidity}%</div>
                  <div className="text-xs text-gray-500">Umidade</div>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Eye className="h-4 w-4 text-gray-400" />
                <div>
                  <div className="text-sm font-medium">{weather.visibility} km</div>
                  <div className="text-xs text-gray-500">Visibilidade</div>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Thermometer className="h-4 w-4 text-gray-400" />
                <div>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium">{weather.uvIndex}</span>
                    <Badge 
                      className={cn(
                        'text-xs text-white px-2 py-0.5',
                        getUVIndexColor(weather.uvIndex)
                      )}
                    >
                      {getUVIndexLabel(weather.uvIndex)}
                    </Badge>
                  </div>
                  <div className="text-xs text-gray-500">Índice UV</div>
                </div>
              </div>
            </div>

            {/* Recomendações */}
            <div className="pt-4 border-t border-gray-100">
              <div className="text-sm font-medium mb-2">Recomendações:</div>
              <div className="space-y-1">
                {weather.uvIndex > 7 && (
                  <div className="text-xs text-orange-600 bg-orange-50 px-2 py-1 rounded">
                    ☀️ Use protetor solar e evite exposição prolongada ao sol
                  </div>
                )}
                {weather.windSpeed > 15 && (
                  <div className="text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded">
                    💨 Condições ideais para kitesurf e windsurf
                  </div>
                )}
                {weather.temperature > 30 && (
                  <div className="text-xs text-red-600 bg-red-50 px-2 py-1 rounded">
                    🌡️ Mantenha-se hidratado e procure sombra
                  </div>
                )}
                {weather.condition === 'sunny' && weather.temperature >= 25 && (
                  <div className="text-xs text-green-600 bg-green-50 px-2 py-1 rounded">
                    🏖️ Dia perfeito para praia!
                  </div>
                )}
              </div>
            </div>
          </>
        )}

        {/* Última atualização */}
        <div className="text-xs text-gray-500 text-center pt-2 border-t border-gray-100">
          Atualizado às {lastUpdated.toLocaleTimeString('pt-BR', { 
            hour: '2-digit', 
            minute: '2-digit' 
          })}
        </div>
      </CardContent>
    </Card>
  );
}
